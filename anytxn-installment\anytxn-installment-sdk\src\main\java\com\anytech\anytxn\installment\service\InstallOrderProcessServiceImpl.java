package com.anytech.anytxn.installment.service;

import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountAbsstatusSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlamsMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherAbsMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherAbsSelfMapper;
import com.anytech.anytxn.business.dao.accounting.mapper.AccountantGlvcherMapper;
import com.anytech.anytxn.business.dao.accounting.model.AccountAbsstatus;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlams;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcher;
import com.anytech.anytxn.business.dao.accounting.model.AccountantGlvcherAbs;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.DateHelper;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountAbsstatusDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlvcherAbsDTO;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlvcherDTO;
import com.anytech.anytxn.business.base.accounting.enums.AbsStatusEnum;
import com.anytech.anytxn.business.base.accounting.enums.AbsTypeEnum;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.mapper.AccountBalanceInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoMapper;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.base.monetary.annotation.BatchSharedAnnotation;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.installment.mapper.InstallOrderMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanMapper;
import com.anytech.anytxn.business.dao.installment.mapper.InstallPlanSelfMapper;
import com.anytech.anytxn.business.dao.installment.model.InstallOrder;
import com.anytech.anytxn.business.dao.installment.model.InstallPlan;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallOrderDTO;
import com.anytech.anytxn.business.base.installment.domain.dto.InstallPlanDTO;
import com.anytech.anytxn.installment.base.domain.dto.InstallParameterDTO;
import com.anytech.anytxn.installment.base.enums.AnyTxnInstallRespCodeEnum;
import com.anytech.anytxn.installment.base.enums.BackOrOutTableEnum;
import com.anytech.anytxn.installment.base.exception.AnyTxnInstallException;
import com.anytech.anytxn.installment.base.enums.InstallRepDetailEnum;
import com.anytech.anytxn.common.sequence.manager.IdGeneratorManager;
import com.anytech.anytxn.installment.base.service.IInstallOrderProcessService;
import com.anytech.anytxn.installment.base.service.IInstallOrderService;
import com.anytech.anytxn.installment.base.service.IInstallPlanService;
import com.anytech.anytxn.installment.base.service.IInstallProductEnquiryService;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallAccountingTransParmResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.monetary.bo.CustAccountParamBO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * ABS申请功能 回退/出表
 *
 * <AUTHOR>
 * @date 2019-12-04
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class InstallOrderProcessServiceImpl implements IInstallOrderProcessService {

    private static final Logger logger = LoggerFactory.getLogger(InstallOrderProcessServiceImpl.class);

    @Autowired
    private InstallOrderMapper installOrderMapper;
    @Autowired
    private InstallPlanSelfMapper installPlanSelfMapper;
    @Autowired
    private AccountBalanceInfoSelfMapper accountBalanceInfoSelfMapper;
    @Autowired
    private InstallPlanMapper installPlanMapper;
    @Autowired
    private AccountantGlvcherAbsSelfMapper tAmsGlvcherAbsSelfMapper;
    @Autowired
    private AccountantGlvcherAbsMapper tAmsGlvcherAbsMapper;
    @Autowired
    private AccountantGlvcherMapper tAmsGlvcherMapper;
    @Autowired
    private AccountAbsstatusSelfMapper accountAbsstatusSelfMapper;
    @Autowired
    private AccountAbsstatusMapper accountAbsstatusMapper;
    @Autowired
    private AccountManagementInfoMapper accountManagementInfoMapper;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private IOrganizationInfoService organizationInfoService;
    @Autowired
    private IInstallProductEnquiryService installProductEnquiryService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private ITransactionTypeService transactionTypeService;
    @Autowired
    private IInstallPlanService installPlanService;
    @Autowired
    private AccountantGlamsMapper amsGlamsMapper;
    @Autowired
    private IInstallOrderService installOrderService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;

    /**
     * 封包(7x24小时批次 【全账户封包处理】 共用, 批次中禁止对数据库有更新操作！！！）
     * @param orderId 分期订单号
     */
    @Override
    public void fengBao(String orderId, String accountManagementId, String productCode, String date, String absType,
                        String source) {
        switch (absType) {
            case "1":
                instFengBao(orderId, productCode);
                break;
            case "2":
                allFengBao(accountManagementId, productCode, date, source);
                break;
            case "3":
                badFengBao(accountManagementId, productCode);
                break;
            default:
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_CURRENT_PACKET_NOT_SUPPORT_FAULT);
        }
    }

    /**
     * 终止（7x24小时批次共用, 批次中禁止对数据库有更新操作！！！）
     *
     * @param accountManagementId 管理账户
     */
    @Override
    public void end(String accountManagementId) {
        // 管理账户
        AccountManagementInfoDTO accManInfo = getManagementInfo(accountManagementId);
        // 当前处理日
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());

        // 终止校验逻辑
        AccountAbsstatus absstatus = getAbsStatus(accManInfo.getAccountManagementId(),
                accManInfo.getOrganizationNumber(),
                accManInfo.getBranchNumber(),
                accManInfo.getAbsProductCodeCurr(),
                AbsTypeEnum.ALL.getCode());
        if (absstatus == null || !StringUtils.equals(absstatus.getAbsStatus(), "F")) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ABS_STATUS_BACK_FAULT);
        }

        //分期订单，分期计划，交易账户，及资产出表会计分录表更新
        backInst(null, accManInfo, BackOrOutTableEnum.BACK, AbsTypeEnum.ALL);
        //abs状态表更新
        endModifyTamsStatus(absstatus, today, accManInfo);
        //管理账户更新
        endModifyAcctMan(accManInfo);
    }

    /**
     * 更新管理账户(批次公用)
     * @param accManInfo
     */
    private void endModifyAcctMan(AccountManagementInfoDTO accManInfo) {
        accManInfo.setAbsType("0");
        accManInfo.setAbsProductCodeFirst(null);
        accManInfo.setAbsProductCodeCurr(null);
        accManInfo.setAbsEndDate(null);

        // 更新
        updateManagementInfo(accManInfo);
    }

    /**
     * 出表
     *
     * @param orderId 分期订单号
     * @param absType
     */
    @Override
    public void outTable(String orderId, String accountManagementId, String absType) {
        switch (absType) {
            case "1":
                instOutTable(orderId);
                break;
            case "2":
                allOutTable(accountManagementId);
                break;
            case "3":
                badOutTable(accountManagementId);
                break;
            default:
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_CURRENT_OUT_TABLE_NOT_SUPPORT_FAULT);
        }
    }


    /**
     * 回退
     *
     * @param orderId 分期订单号
     * @param absType
     */
    @Override
    public void back(String orderId, String accountManagementId, String absType) {
        switch (absType) {
            case "1":
                instBack(orderId);
                break;
            case "2":
                allBack(accountManagementId);
                break;
            case "3":
                badBack(accountManagementId);
                break;
            default:
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_CURRENT_BACK_NOT_SUPPORT_FAULT);
        }
    }

    private void badBack(String accountManagementId) {
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        checkManAbsType(accManInfo, BackOrOutTableEnum.BACK, "3");
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        AccountAbsstatus tAmsAbsStatus = checkAbsDate(accManInfo, null, "3");
        updateTamsAbsStatus(BackOrOutTableEnum.BACK, today, tAmsAbsStatus, accManInfo, AbsTypeEnum.BAD);
        backInst(null, accManInfo, BackOrOutTableEnum.BACK, AbsTypeEnum.BAD);
        //管理账户更新
        updateAcctMan(accManInfo, "3");
    }

    /**
     * 检查封包日期
     * @param accManInfo
     * @param installOrder
     * @param absType
     * @return
     */
    private AccountAbsstatus checkAbsDate(AccountManagementInfoDTO accManInfo, InstallOrderDTO installOrder, String absType) {
        // 机构
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accManInfo.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accManInfo.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}, result={}", accManInfo.getOrganizationNumber(), organizationInfo != null ? "found" : "not found");
        // abs状态表
        AccountAbsstatus tAmsAbsStatus = findTamsAbsStatus(accManInfo, installOrder, absType);
        // 封包日期 != 下一处理日
        if (organizationInfo.getNextProcessingDay().compareTo(tAmsAbsStatus.getPreabsDate()) == 0) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_PACKET_ON_THE_DAY_FAULT);
        }
        return tAmsAbsStatus;
    }

    /**
     * 检查管理账户abs状态
     * @param accManInfo
     * @param enums
     * @param absType
     */
    private void checkManAbsType(AccountManagementInfoDTO accManInfo, BackOrOutTableEnum enums, String absType) {
        if (!StringUtils.equals(accManInfo.getAbsType(), absType)) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ACCT_ABS_STATUS_FAULT);
        }
        if (enums == BackOrOutTableEnum.FENBAO || !StringUtils.equalsAny(absType, "2", "3")) {
            return;
        }
        AccountAbsstatus absstatus = accountAbsstatusSelfMapper.selectOneByCondition(accManInfo.getAccountManagementId(),
                accManInfo.getOrganizationNumber(),
                accManInfo.getAbsProductCodeCurr(),
                null,
                absType,
                "110110");
        if (enums == BackOrOutTableEnum.BACK) {
            if (absstatus == null || !StringUtils.equals(absstatus.getAbsStatus(), "F")) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ABS_STATUS_BACK_FAULT);
            }
        }
        if (enums == BackOrOutTableEnum.OUT_TABLE) {
            if (absstatus == null || !StringUtils.equals(absstatus.getAbsStatus(), "F")) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ABS_STATUS_OUT_TABLE_FAULT);
            }
        }
    }

    private void allBack(String accountManagementId) {
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        checkManAbsType(accManInfo, BackOrOutTableEnum.BACK, "2");
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        AccountAbsstatus tAmsAbsstatus = checkAbsDate(accManInfo, null, "2");
        updateTamsAbsStatus(BackOrOutTableEnum.BACK, today, tAmsAbsstatus, accManInfo, AbsTypeEnum.ALL);

        backInst(null, accManInfo, BackOrOutTableEnum.BACK, AbsTypeEnum.ALL);
        //管理账户更新
        updateAcctMan(accManInfo, "2");
    }

    private void instBack(String orderId) {
        List<InstallOrderDTO> orderList = getOrder(orderId, null, BackOrOutTableEnum.BACK);
        InstallOrderDTO order = orderList.get(0);
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(order.getAccountManagementId());
        checkManAbsType(accManInfo, BackOrOutTableEnum.BACK, "1");
        backInst(orderId, null, BackOrOutTableEnum.BACK, AbsTypeEnum.INSTALL);
        //管理账户更新
        updateAcctMan(accManInfo, "1");
    }

    /**
     * abs回退/终止 (7x24小时批次共用, 只处理全账户abs)
     * @param orderId                   订单号
     * @param accountManagementInfo     管理账户
     * @param back                      出表/回退
     * @param absType                   abs类型
     */
    private void backInst(String orderId,
                          AccountManagementInfoDTO accountManagementInfo,
                          BackOrOutTableEnum back,
                          AbsTypeEnum absType) {
        // 获取分期订单
        List<InstallOrderDTO> orderList = getOrder(orderId, accountManagementInfo, back);
        if (!CollectionUtils.isEmpty(orderList)) {
            // 分期abs处理（7x24不涉及暂时不处理）
            if (Objects.equals(AbsTypeEnum.INSTALL.getCode(), absType.getCode())) {
                InstallOrderDTO order = orderList.get(0);
                //更新abs状态表的出表日期，abs状态
                AccountManagementInfoDTO accountManagementInfo2 = getManagementInfo(order.getAccountManagementId());
                accountManagementInfo2.setOrganizationNumber(order.getOrganizationNumber());
                LocalDate today = getToday(accountManagementInfo2.getCustomerId(), accountManagementInfo2.getOrganizationNumber());
                // abs封包日期校验
                AccountAbsstatus tAmsAbsstatus = checkAbsDate(accountManagementInfo2, order, AbsTypeEnum.INSTALL.getCode());
                // 更新abs状态表
                updateTamsAbsStatus(back, today, tAmsAbsstatus, accountManagementInfo2, absType);
            }
            for (InstallOrderDTO order : orderList) {
                // 校验abs
                checkAbsStatus(absType, order);
                // 订单id
                orderId = order.getOrderId();
                // 订单资产编号
                String absProductCode = order.getAbsProductCode();
                //更新订单表
                updateInstallOrder(order, back.getCode());
                //更新计划表
                updateInstallPlan(orderId, back.getCode());
                //更新交易账户表
                updateAccountBalanceInfo(order.getAccountManagementId(), String.valueOf(orderId), back.getCode(), absType);
                //更新会计分录表
                updateTamsGlvcherAbs(order.getAccountManagementId(), absProductCode, String.valueOf(orderId), back.getCode());

                //更新交易账户表
                updateAccountBalanceInfo(order.getAccountManagementId(), "0", back.getCode(), absType);
                //更新会计分录表
                updateTamsGlvcherAbs(order.getAccountManagementId(), absProductCode, "0", back.getCode());
            }
        } else {
            if (StringUtils.equalsAny(absType.getCode(), AbsTypeEnum.ALL.getCode(), AbsTypeEnum.BAD.getCode())) {
                //更新交易账户表
                updateAccountBalanceInfo(accountManagementInfo.getAccountManagementId(), "0", back.getCode(), absType);
                //更新会计分录表
                updateTamsGlvcherAbs(accountManagementInfo.getAccountManagementId(), accountManagementInfo.getAbsProductCodeCurr(), "0", back.getCode());
            }
        }
    }

    /**
     * abs校验
     * @param absType
     * @param order
     */
    private void checkAbsStatus(AbsTypeEnum absType, InstallOrderDTO order) {
        // 分期abs且订单abs不是封包状态，抛出异常
        if (Objects.equals(AbsTypeEnum.INSTALL.getCode(), absType.getCode()) && !Objects.equals(AbsStatusEnum.F.getCode(), order.getAbsStatus())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ABS_STATUS_NOT_CORRECT_FAULT);
        }
    }


    private void instOutTable(String orderId) {
        List<InstallOrderDTO> orderList = getOrder(orderId, null, BackOrOutTableEnum.OUT_TABLE);
        InstallOrderDTO order = orderList.get(0);
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(order.getAccountManagementId());
        checkManAbsType(accManInfo, BackOrOutTableEnum.OUT_TABLE, "1");
        //出表
        backInst(orderId, null, BackOrOutTableEnum.OUT_TABLE, AbsTypeEnum.INSTALL);
    }

    private void badOutTable(String accountManagementId) {
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        checkManAbsType(accManInfo, BackOrOutTableEnum.OUT_TABLE, "3");
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        //更新abs状态表的出表日期，abs状态
        AccountAbsstatus tAmsAbsstatus = checkAbsDate(accManInfo, null, "3");
        updateTamsAbsStatus(BackOrOutTableEnum.OUT_TABLE, today, tAmsAbsstatus, accManInfo, AbsTypeEnum.BAD);
        //非空判断
        backInst(null, accManInfo, BackOrOutTableEnum.OUT_TABLE, AbsTypeEnum.BAD);
    }

    private void allOutTable(String accountManagementId) {
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        checkManAbsType(accManInfo, BackOrOutTableEnum.OUT_TABLE, "2");
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        //更新abs状态表的出表日期，abs状态
        AccountAbsstatus tAmsAbsstatus = checkAbsDate(accManInfo, null, "2");
        updateTamsAbsStatus(BackOrOutTableEnum.OUT_TABLE, today, tAmsAbsstatus, accManInfo, AbsTypeEnum.ALL);
        //非空判断
        backInst(null, accManInfo, BackOrOutTableEnum.OUT_TABLE, AbsTypeEnum.ALL);
        modifyAcctMan(accManInfo, "2");
    }

    /**
     * 更新abs状态表（批次共用）
     * @param amsAbsstatus 待更新abs状态表
     * @param today
     * @param accManInfo
     */
    private void endModifyTamsStatus(AccountAbsstatus amsAbsstatus, LocalDate today, AccountManagementInfoDTO accManInfo) {
        amsAbsstatus.setAbsStatus("N");
        amsAbsstatus.setRollbackAbsDate(today);
        amsAbsstatus.setStatus("1");

        // 更新
        updateAbsStatus(amsAbsstatus);

        // 加载首次资产编号对应的资产状态表
        AccountAbsstatus firstAbsstatus = getAbsStatus(accManInfo.getAccountManagementId(),
                accManInfo.getOrganizationNumber(),
                accManInfo.getBranchNumber(),
                accManInfo.getAbsProductCodeFirst(),
                AbsTypeEnum.ALL.getCode());
        if (firstAbsstatus == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TAMS_ABS_STATUS_NOT_EXIST_FAULT);
        }
        firstAbsstatus.setRollbackAbsDate(today);
        firstAbsstatus.setStatus("1");
        firstAbsstatus.setEndabsDate(today);
        if (StringUtils.equals(accManInfo.getAbsProductCodeCurr(),accManInfo.getAbsProductCodeFirst())){
            firstAbsstatus.setAcctAbsStage("3");
        }

        // 更新
        updateAbsStatus(firstAbsstatus);
    }

    /**
     * 获取当前处理日（批中共用）
     * 如果客户对账日期 = 当前累计日， 取下一处理日（客户已处理，当前处理属于下一处理日处理）
     * 如果客户对账日期 < 当前累计日， 取当前处理日（客户未处理，当前处理本次处理日处理）
     * @oaram customerId    客户id
     * @param org           机构号
     * @return
     */
    private LocalDate getToday(String customerId, String org) {
        // 是否7x24批
        Boolean isbatch = CustAccountBO.isBatch();
        // 机构
        OrganizationInfoResDTO organizationInfo;
        // 客户对账表
        CustReconciliationControlDTO control;

        if (isbatch) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            organizationInfo = custAccountBO.getParams().getOrganizationInfoResDTO();
            control = custAccountBO.getCustReconciliationControl();
        } else {
            logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", org);
            organizationInfo = organizationInfoService.findOrganizationInfo(org);
            logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}, result={}", org, organizationInfo != null ? "found" : "not found");

            logger.info("Calling custReconciliationControlService.getControl: customerId={}, organizationNumber={}", customerId, org);
            control = custReconciliationControlService.getControl(customerId, org);
            logger.info("custReconciliationControlService.getControl completed: customerId={}, organizationNumber={}, result={}", customerId, org, control != null ? "found" : "not found");
        }

        if (control.getReconciliationDate().isEqual(organizationInfo.getAccruedThruDay())) {
            return organizationInfo.getNextProcessingDay();
        } else if (control.getReconciliationDate().isBefore(organizationInfo.getAccruedThruDay())) {
            return organizationInfo.getToday();
        } else{
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_PARAM_ERROR, InstallRepDetailEnum.BU_RE);
        }
    }

    private void updateAcctMan(AccountManagementInfoDTO accManInfo, String absType) {
        accManInfo.setUpdateTime(LocalDateTime.now());
        accManInfo.setVersionNumber(accManInfo.getVersionNumber() + 1);
        switch (absType) {
            case "1":
                List<AccountAbsstatus> amsAbsstatusList = accountAbsstatusSelfMapper.selectByCondition(accManInfo.getAccountManagementId(),
                        null,
                        null,
                        AbsTypeEnum.INSTALL.getCode());
                boolean result = amsAbsstatusList == null || amsAbsstatusList.stream().map(AccountAbsstatus::getAbsStatus).allMatch("N"::equals);
                if (result) {
                    accManInfo.setAbsType("0");
                    accManInfo.setAbsProductCodeFirst(null);
                    accManInfo.setAbsProductCodeCurr(null);
                }
                break;
            case "2":
                if (StringUtils.equals(accManInfo.getAbsProductCodeFirst(),
                        accManInfo.getAbsProductCodeCurr())) {
                    accManInfo.setAbsType("0");
                    accManInfo.setAbsProductCodeFirst(null);
                    accManInfo.setAbsProductCodeCurr(null);
                    accManInfo.setAbsEndDate(null);
                } else {
                    modifyAcctMan(accManInfo, "2");
                }
                break;
            case "3":
                accManInfo.setAbsType("0");
                accManInfo.setAbsProductCodeFirst(null);
                accManInfo.setAbsProductCodeCurr(null);
                accManInfo.setAbsEndDate(null);
                break;
            default:
                break;
        }
        AccountManagementInfo accountManagementInfo = BeanMapping.copy(accManInfo, AccountManagementInfo.class);
        accountManagementInfoMapper.updateByPrimaryKey(accountManagementInfo);
    }

    private void modifyAcctMan(AccountManagementInfoDTO accountManagementInfoDTO, String absType) {
        LocalDate today = getToday(accountManagementInfoDTO.getCustomerId(), accountManagementInfoDTO.getOrganizationNumber());
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accountManagementInfoDTO.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo =
                organizationInfoService.findOrganizationInfo(accountManagementInfoDTO.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}, result={}", accountManagementInfoDTO.getOrganizationNumber(), organizationInfo != null ? "found" : "not found");
        LocalDate accruedThruDay = organizationInfo.getAccruedThruDay();
        LocalDate absEndDate = accountManagementInfoDTO.getAbsEndDate();
        if (absEndDate.compareTo(accruedThruDay) > 0) {
            if (accountManagementInfoDTO.getAbsProductCodeCurr() != null) {
                BigDecimal bigDecimal = new BigDecimal(accountManagementInfoDTO.getAbsProductCodeCurr());
                BigDecimal add = bigDecimal.add(BigDecimal.ONE);
                accountManagementInfoDTO.setAbsProductCodeCurr(add.toEngineeringString());
                AccountManagementInfo accountManagementInfo = BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class);
                int i = accountManagementInfoSelfMapper.updateByPrimaryKeySelective(accountManagementInfo);
                if (i == 0) {
                    throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT, InstallRepDetailEnum.UP_MA);
                }
            }
            addAbsStatus(accountManagementInfoDTO, today, absType, accountManagementInfoDTO.getAbsProductCodeCurr(), "0", null);
        }
    }

    /**
     * 查询abs状态表
     * @param accManInfo        管理账户
     * @param installOrder      订单
     * @param absType           abs类型
     * @return
     */
    private AccountAbsstatus findTamsAbsStatus(AccountManagementInfoDTO accManInfo, InstallOrderDTO installOrder, String absType) {
        AccountAbsstatus amsAbsstatus = null;
        switch (absType) {
            // 分期abs
            case "1":
                amsAbsstatus = accountAbsstatusSelfMapper.selectOneByCondition(installOrder.getAccountManagementId(),
                        null,
                        installOrder.getAbsProductCode(),
                        String.valueOf(installOrder.getOrderId()),
                        absType,
                        null);
                break;
            // 全账户abs
            case "2":
            // 不良abs
            case "3":
                amsAbsstatus = accountAbsstatusSelfMapper.selectOneByCondition(accManInfo.getAccountManagementId(),
                        accManInfo.getOrganizationNumber(),
                        accManInfo.getAbsProductCodeCurr(),
                        null,
                        absType,
                        accManInfo.getBranchNumber());
                break;
            default:
                break;
        }
        if (amsAbsstatus == null) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_TAMS_ABS_STATUS_NOT_EXIST_FAULT);
        }
        return amsAbsstatus;
    }

    /**
     * 更新abs状态表
     * @param flag          abs操作类型
     * @param today         当前处理日
     * @param amsAbsStatus  原abs状态表
     * @param accManInfo    管理账户号
     * @param absType       abs类型
     */
    private void updateTamsAbsStatus(BackOrOutTableEnum flag,
                                     LocalDate today,
                                     AccountAbsstatus amsAbsStatus,
                                     AccountManagementInfoDTO accManInfo,
                                     AbsTypeEnum absType) {
        AccountAbsstatus newStatus = new AccountAbsstatus();
        if (CustAccountBO.isBatch()) {
            BeanMapping.copy(amsAbsStatus, newStatus);
        }
        newStatus.setId(amsAbsStatus.getId());
        //回退
        if (flag == BackOrOutTableEnum.BACK) {
            newStatus.setAbsStatus("N");
            newStatus.setRollbackAbsDate(today);
            newStatus.setStatus("1");
            // 设置全账户abs阶段
            if (Objects.equals(absType.getCode(), AbsTypeEnum.ALL.getCode())
                    && StringUtils.equals(accManInfo.getAbsProductCodeCurr(),accManInfo.getAbsProductCodeFirst())){
                newStatus.setAcctAbsStage("0");
            }
        //出表
        } else if (flag == BackOrOutTableEnum.OUT_TABLE) {
            newStatus.setAbsStatus("A");
            newStatus.setOutabsDate(today);
            // 设置全账户abs阶段
            if (Objects.equals(absType.getCode(), AbsTypeEnum.ALL.getCode())
                    && StringUtils.equals(accManInfo.getAbsProductCodeCurr(),accManInfo.getAbsProductCodeFirst())){
                newStatus.setAcctAbsStage("2");
            }
        }
        newStatus.setUpdateTime(LocalDateTime.now());
        newStatus.setVersionNumber(amsAbsStatus.getVersionNumber() + 1);

        // 7x24批次先记录不更新
        if (CustAccountBO.isBatch()) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            custAccountBO.addTamsAbsStatus(BeanMapping.copy(newStatus, AccountAbsstatusDTO.class));
        } else {
            accountAbsstatusMapper.updateByPrimaryKeySelective(newStatus);
        }
    }

    /**
     * 基于订单id获取订单
     *
     * @param orderId 分期订单号
     * @return InstallOrder
     */
    private InstallOrder getInstallOrderById(String orderId) {
        InstallOrder installOrder = installOrderMapper.selectByPrimaryKey(orderId);
        if (installOrder == null) {
            logger.error("Install order not found: orderId={}", orderId);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INST_PRO_PARAM_NOT_EXIST_FAULT);
        }
        return installOrder;
    }

    /**
     * 更新分期订单表 (7x24批次公共)
     *
     * @param installOrder 分期订单
     * @param installEnum  回算/出表枚举值
     */
    private void updateInstallOrder(InstallOrderDTO installOrder, String installEnum) {
        if (Objects.equals(installEnum, BackOrOutTableEnum.BACK.getCode())) {
            installOrder.setAbsStatus("N");
            installOrder.setAbsProductCode(null);
        }
        if (Objects.equals(installEnum, BackOrOutTableEnum.OUT_TABLE.getCode())) {
            installOrder.setAbsStatus("A");
        }

        // 更新
        updateInstallOrder(installOrder);
    }

    /**
     * 更新分期计划表(批次共用)
     *
     * @param orderId     分期订单号
     * @param installEnum 回算/出表枚举值
     */
    private void updateInstallPlan(String orderId, String installEnum) {
        List<InstallPlan> installPlanList = installPlanSelfMapper.selectPlansByOrderId(orderId);

        if (CollectionUtils.isNotEmpty(installPlanList)) {
            List<InstallPlanDTO> installPlans = BeanMapping.copyList(installPlanList, InstallPlanDTO.class);
            // 批中检查是否计划已更新
            if (CustAccountBO.isBatch()) {
                installPlans = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(installPlans, orderId);
            }

            installPlans.forEach(plan -> {
                if ("F".equals(plan.getAbsStatus())) {
                    if (Objects.equals(BackOrOutTableEnum.BACK.getCode(), installEnum)) {
                        plan.setAbsStatus("N");
                        plan.setAbsProductCode(null);
                    }
                    if (Objects.equals(BackOrOutTableEnum.OUT_TABLE.getCode(), installEnum)) {
                        plan.setAbsStatus("A");
                    }

                    // 更新
                    updateInstallPlan(plan);
                }
            });
        }
    }

    /**
     * 更新交易账户 (7x24小时批次共用)
     *
     * @param accManageId 管理账户号
     * @param orderId     分期订单号
     * @param installEnum abs操作类型
     * @param absType     abs类型
     */
    private void updateAccountBalanceInfo(String accManageId, String orderId, String installEnum, AbsTypeEnum absType) {
        // 是否批中
        Boolean isBatch = CustAccountBO.isBatch();
        CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
        // 更新管理账户下交易账户状态
        if (isBatch) {
            AccountManagementInfoDTO managementInfo = custAccountBO.getCustomerBO().getManagementById(accManageId);
            List<AccountBalanceInfoDTO> accountBalanceInfos = custAccountBO.getCustomerBO().getBalanceByManagementId(managementInfo.getAccountManagementId());

            if (CollectionUtils.isNotEmpty(accountBalanceInfos)) {
                for (AccountBalanceInfoDTO balanceInfo : accountBalanceInfos) {
                    if (balanceInfo.getInstallmentOrderNumber() != null
                            && ableUpdateBalance(managementInfo.getAbsProductCodeCurr(), balanceInfo, orderId, installEnum, absType)) {
                        // >> 更新容器中的交易账户
                        custAccountBO.getCustomerBO().updateBalanceInfo(balanceInfo, true);
                    }
                }
            }
        } else {
            AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accManageId);
            List<AccountBalanceInfo> accountBalanceInfos = accountBalanceInfoSelfMapper.selectAccountBalanceInfoByManagementId(accManageId, PartitionKeyUtils.partitionKey(accManInfo.getCustomerId()));
            if (CollectionUtils.isNotEmpty(accountBalanceInfos)) {
                for (AccountBalanceInfo balanceInfo : accountBalanceInfos) {
                    AccountBalanceInfoDTO balanceInfoDTO = BeanMapping.copy(balanceInfo, AccountBalanceInfoDTO.class);
                    if (balanceInfo.getInstallmentOrderNumber() != null
                            && ableUpdateBalance(accManInfo.getAbsProductCodeCurr(), balanceInfoDTO, orderId, installEnum, absType)) {
                        int i = accountBalanceInfoSelfMapper.updateByOptLock(BeanMapping.copy(balanceInfoDTO, AccountBalanceInfo.class));
                        if (i == 0) {
                            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT, InstallRepDetailEnum.UP_MA);
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断是否更新交易账户，并更新abs状态和资产编号
     * @param managementAbsProductCode
     * @param balanceInfo
     * @param orderId
     * @param installEnum
     * @param absType
     * @return 是否更新
     */
    private Boolean ableUpdateBalance(String managementAbsProductCode, AccountBalanceInfoDTO balanceInfo, String orderId, String installEnum, AbsTypeEnum absType){
        String installmentOrderNumber = balanceInfo.getInstallmentOrderNumber();
        boolean flag = Objects.equals(orderId, installmentOrderNumber)
                && !Objects.equals(AbsStatusEnum.N.getCode(), balanceInfo.getAbsStatus())
                && !StringUtils.isBlank(balanceInfo.getAbsProductCode());
        if (AbsTypeEnum.ALL.getCode().equals(absType.getCode()) || AbsTypeEnum.BAD.getCode().equals(absType.getCode())) {
            flag = Objects.equals(orderId, installmentOrderNumber)
                    && !"N".equals(balanceInfo.getAbsStatus())
                    && StringUtils.equals(balanceInfo.getAbsProductCode(),
                    managementAbsProductCode);
        }
        if (flag) {
            if (Objects.equals(BackOrOutTableEnum.OUT_TABLE.getCode(), installEnum)) {
                balanceInfo.setAbsStatus("A");
            }
            if (Objects.equals(BackOrOutTableEnum.BACK.getCode(), installEnum)) {
                balanceInfo.setAbsStatus("N");
                balanceInfo.setAbsProductCode(null);
            }
        }

        return flag;
    }

    /**
     * 更新会计资产传票表（7x24小时批次共用）
     *
     * @param accountManagementId 管理账户
     * @param absProductCode      资产编号
     * @param orderId             分期订单号
     * @param installEnum         回算/出表枚举值
     */
    private void updateTamsGlvcherAbs(String accountManagementId, String absProductCode, String orderId, String installEnum) {
        AccountManagementInfoDTO managementInfoDTO = getAccountManagementInfoDTO(accountManagementId);
        // 如果批中也查询库，同一账户下资产会计传票不会多次处理
        List<AccountantGlvcherAbs> tAmsGlvcherAbs = tAmsGlvcherAbsSelfMapper.selectByOrderId(managementInfoDTO.getOrganizationNumber(),
                accountManagementId, orderId);
        // 管理账户(批中从缓存获取)
        if (CollectionUtils.isNotEmpty(tAmsGlvcherAbs)) {
            for (AccountantGlvcherAbs abs : tAmsGlvcherAbs) {
                if (Objects.equals(absProductCode, abs.getAssetNo())) {
                    if (Objects.equals(BackOrOutTableEnum.BACK.getCode(), installEnum)) {
                        //已退回
                        abs.setProcessType("3");
                    }
                    if (Objects.equals(BackOrOutTableEnum.OUT_TABLE.getCode(), installEnum)) {
                        // 写入会计传票表
                        AccountantGlvcher tAmsGlvcher = BeanMapping.copy(abs, AccountantGlvcher.class);

                        try {
                            LocalDate today = getToday(managementInfoDTO.getCustomerId(), tAmsGlvcher.getOrganizationNumber());
                            tAmsGlvcher.setPostingDate(today);

                            // 插入
                            insertTamsGlvcher(tAmsGlvcher);
                        } catch (Exception e) {
                            logger.error("Failed to insert accounting voucher: accountManagementId={}, orderId={}", accountManagementId, orderId, e);
                            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_INSERT_DATABASE_FAULT);
                        }

                        // 标记原传票已处理
                        abs.setProcessType("2");
                    }

                    // 更新
                    updateTamsGlvcherAbs(abs);
                }
            }
        }
    }

    /**
     * 不良封包
     * @param accountManagementId
     * @param productCode
     */
    private void badFengBao(String accountManagementId, String productCode) {
        checkProductCode(productCode, "3");
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        checkManAbsType(accManInfo, BackOrOutTableEnum.FENBAO, "0");
        if (!"1".equals(accManInfo.getFinanceStatus())) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCT_FINANCE_STATUS_FAULT);
        }
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        List<InstallOrderDTO> orders = getOrder(null, accManInfo, BackOrOutTableEnum.FENBAO);
        //封包分期处理
        fengBaoInst(orders, accManInfo, productCode, today, "3", "1");
        //交易账户预出表
        List<AccountBalanceInfoDTO> accountBalanceInfos = null;
        try {
            logger.info("Calling transactionTypeService.findNoOv999: organizationNumber={}", accManInfo.getOrganizationNumber());
            List<TransactionTypeResDTO> transactionTypeResDTOS = transactionTypeService.findNoOv999(accManInfo.getOrganizationNumber());
            logger.info("transactionTypeService.findNoOv999 completed: organizationNumber={}, resultSize={}", accManInfo.getOrganizationNumber(), transactionTypeResDTOS != null ? transactionTypeResDTOS.size() : 0);
            List<String> codes = transactionTypeResDTOS.stream().map(TransactionTypeResDTO::getTransactionTypeCode).collect(Collectors.toList());

            if (CustAccountBO.isBatch()) {
                accountBalanceInfos = CustAccountBO.threadCustAccountBO.get().getCustomerBO()
                        .getBalanceByManagementId(accManInfo.getAccountManagementId())
                        .stream()
                        .filter(x -> x.getAbsStatus().equals(AbsStatusEnum.N.getCode())
                                && x.getBalance().compareTo(BigDecimal.ZERO) > 0
                                && codes.contains(x.getTransactionTypeCode()))
                        .collect(Collectors.toList());
            } else {
                List<AccountBalanceInfo> balanceInfoList = accountBalanceInfoSelfMapper.selectFengBaoByTransactionType(accManInfo.getAccountManagementId(), accManInfo.getOrganizationNumber(), codes);
                if (CollectionUtils.isNotEmpty(balanceInfoList)) {
                    accountBalanceInfos = BeanMapping.copyList(balanceInfoList, AccountBalanceInfoDTO.class);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to select account balance info by transaction type: accountManagementId={}, organizationNumber={}", accManInfo.getAccountManagementId(), accManInfo.getOrganizationNumber(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }

        if (!CollectionUtils.isEmpty(accountBalanceInfos)) {
            readyOutAcctBal(productCode, accManInfo, today, accountBalanceInfos, "3");
        }
        //管理账户更新
        handleAcctMan(accManInfo, "3", productCode, null);
        //Abs状态表增加
        addAbsStatus(accManInfo, today, "3", productCode, "0", null);
    }

    @BatchSharedAnnotation
    @Override
    public void badAbsToOrder(String accountManagementId, String productCode) {
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(accountManagementId);
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        List<InstallOrderDTO> orders = getOrder(null, accManInfo, BackOrOutTableEnum.FENBAO);
        //封包分期处理
        fengBaoInst(orders, accManInfo, productCode, today, "2", "1");
    }

    /**
     * 加载分期订单（7x24批次公用逻辑）
     * @param orderId                   订单号(可能是null)
     * @param accountManagementInfoDTO  管理账户(可能是null)
     * @param backOrOut                 abs操作类型
     * @return
     */
    @BatchSharedAnnotation
    private List<InstallOrderDTO> getOrder(String orderId,
                                        AccountManagementInfoDTO accountManagementInfoDTO,
                                        BackOrOutTableEnum backOrOut) {
        List<InstallOrderDTO> installOrders = Lists.emptyList();

        // 是否7x24批中
        Boolean isBatch = CustAccountBO.isBatch();
        if (accountManagementInfoDTO != null) {
            logger.info("Calling installOrderService.selectByOrgNumAndAcctManageId: organizationNumber={}, accountManagementId={}", accountManagementInfoDTO.getOrganizationNumber(), accountManagementInfoDTO.getAccountManagementId());
            List<InstallOrderDTO> installOrderDtos = installOrderService.selectByOrgNumAndAcctManageId(accountManagementInfoDTO.getOrganizationNumber(), accountManagementInfoDTO.getAccountManagementId());
            logger.info("installOrderService.selectByOrgNumAndAcctManageId completed: organizationNumber={}, accountManagementId={}, resultSize={}", accountManagementInfoDTO.getOrganizationNumber(), accountManagementInfoDTO.getAccountManagementId(), installOrderDtos != null ? installOrderDtos.size() : 0);

            // 批中需要检查是否有存在已更新订单，如果有进行更新操作
            if (isBatch) {
                installOrderDtos = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallOrders(installOrderDtos, x-> x.getAccountManagementId().equals(accountManagementInfoDTO.getAccountManagementId()));
            }

            // 基于abs操作筛选订单
            if (backOrOut == BackOrOutTableEnum.FENBAO) {
                installOrders = installOrderDtos.stream()
                        // 筛选分期asb且未入账金额>0的订单
                        .filter(installOrderDTO ->  StringUtils.equals(installOrderDTO.getStatus(), AbsTypeEnum.INSTALL.getCode()) && BigDecimal.ZERO.compareTo(installOrderDTO.getUnpostedAmount()) < 0)
                        .collect(Collectors.toList());
            } else {
                installOrders = installOrderDtos.stream()
                        // 筛选abs资产编号 = 管理账户abs资产编号的订单
                        .filter(installOrderDTO -> StringUtils.equals(installOrderDTO.getAbsProductCode(), accountManagementInfoDTO.getAbsProductCodeCurr()))
                        .collect(Collectors.toList());
            }
        } else if (orderId != null) {
            installOrders = Lists.newArrayList(BeanMapping.copy(getInstallOrderById(orderId), InstallOrderDTO.class));

            // 批中需要检查是否有存在已更新订单，如果有进行更新操作
            if (isBatch) {
               installOrders = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallOrders(installOrders, x-> x.getOrderId().equals(orderId));
            }
        }
        return installOrders;
    }

    /**
     * 全账户封包处理（批次公用）
     * @param accountManagementId
     * @param productCode
     * @param date
     * @param source
     */
    private void allFengBao(String accountManagementId, String productCode, String date, String source) {
        // 获取管理账户
        AccountManagementInfoDTO accManInfo = getManagementInfo(accountManagementId);
        // 获取当前累计日
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        // 获取机构
        logger.info("Calling organizationInfoService.findOrganizationInfo: organizationNumber={}", accManInfo.getOrganizationNumber());
        OrganizationInfoResDTO organizationInfo = organizationInfoService.findOrganizationInfo(accManInfo.getOrganizationNumber());
        logger.info("organizationInfoService.findOrganizationInfo completed: organizationNumber={}, result={}", accManInfo.getOrganizationNumber(), organizationInfo != null ? "found" : "not found");
        // 获取下一处理日
        LocalDate nextDay = organizationInfo.getNextProcessingDay();

        if ("1".equals(source)) {
            // 检查资产编号
            checkProductCode(productCode, "2");
            // 检查管理账户的abs状态
            checkManAbsType(accManInfo, BackOrOutTableEnum.FENBAO, "0");
            if (date == null || DateHelper.toLocalDate(date).compareTo(nextDay) <= 0) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_END_ABS_DATE_NOT_CORRECT_FAULT);
            }
        }

        // 获取分期订单
        List<InstallOrderDTO> orders = getOrder(null, accManInfo, BackOrOutTableEnum.FENBAO);
        // 封包分期处理
        fengBaoInst(orders, accManInfo, productCode, today, "2", source);
        // 交易账户预出表
        List<AccountBalanceInfoDTO> accountBalanceInfos = null;
        try {
            // 分期订单号为空或者0，并且abs状态为N，并且资产包编号为空，交易账户余额大于0，并且交易账户的交易类型对应的余额类型为本金
            logger.info("Calling transactionTypeService.findBalType1: organizationNumber={}", accManInfo.getOrganizationNumber());
            List<TransactionTypeResDTO> transactionTypeResDTOS = transactionTypeService.findBalType1(accManInfo.getOrganizationNumber());
            logger.info("transactionTypeService.findBalType1 completed: organizationNumber={}, resultSize={}", accManInfo.getOrganizationNumber(), transactionTypeResDTOS != null ? transactionTypeResDTOS.size() : 0);
            List<String> codes = transactionTypeResDTOS.stream().map(TransactionTypeResDTO::getTransactionTypeCode).collect(Collectors.toList());

            if (CustAccountBO.isBatch()) {
                accountBalanceInfos = CustAccountBO.threadCustAccountBO.get().getCustomerBO()
                        .getBalanceByManagementId(accManInfo.getAccountManagementId())
                        .stream()
                        .filter(x -> x.getAbsStatus().equals(AbsStatusEnum.N.getCode())
                                && x.getBalance().compareTo(BigDecimal.ZERO) > 0
                                && x.getAbsProductCode() == null
                                && codes.contains(x.getTransactionTypeCode()))
                        .collect(Collectors.toList());
            } else {
                List<AccountBalanceInfo> balanceInfoList = accountBalanceInfoSelfMapper.selectFengBaoByTransactionType(accManInfo.getAccountManagementId(), accManInfo.getOrganizationNumber(), codes);
                if (CollectionUtils.isNotEmpty(balanceInfoList)) {
                    accountBalanceInfos = BeanMapping.copyList(balanceInfoList, AccountBalanceInfoDTO.class);
                }
            }

            // 筛选分期订单号为空或者0
            accountBalanceInfos = accountBalanceInfos.stream().filter(x-> StringUtils.isBlank(x.getInstallmentOrderNumber()) || "0".equals(x.getInstallmentOrderNumber())).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to select account balance info for all package: accountManagementId={}, organizationNumber={}", accManInfo.getAccountManagementId(), accManInfo.getOrganizationNumber(), e);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
        if (!CollectionUtils.isEmpty(accountBalanceInfos)) {
            List<AccountBalanceInfoDTO> list =
                    accountBalanceInfos
                            .stream()
                            .filter(x-> StringUtils.isBlank(x.getInstallmentOrderNumber()) || "0".equals(x.getInstallmentOrderNumber()))
                            .collect(Collectors.toList());
            // 更新交易账户
            readyOutAcctBal(productCode, accManInfo, today, list, "2");
        }
        //管理账户更新
        if (!"2".equals(source)) {
            // 更新管理账户
            handleAcctMan(accManInfo, "2", productCode, date);
            //Abs状态表增加
            addAbsStatus(accManInfo, today, "2", productCode, "0", "1");
        }
    }

    /**
     * 更新交易账户（批中共用）
     * @param productCode
     * @param accManInfo
     * @param today
     * @param accountBalanceInfoDTOList
     * @param s
     */
    private void readyOutAcctBal(String productCode, AccountManagementInfoDTO accManInfo, LocalDate today, List<AccountBalanceInfoDTO> accountBalanceInfoDTOList, String s) {
        accountBalanceInfoDTOList.forEach(accountBalanceInfoDTO -> {
            accountBalanceInfoDTO.setAbsStatus("F");
            accountBalanceInfoDTO.setAbsProductCode(productCode);
            AccountBalanceInfo accountBalanceInfo = new AccountBalanceInfo();
            accountBalanceInfo.setAbsStatus("F");
            accountBalanceInfo.setAbsProductCode(productCode);
            accountBalanceInfo.setTransactionBalanceId(accountBalanceInfoDTO.getTransactionBalanceId());
            accountBalanceInfo.setUpdateTime(LocalDateTime.now());
            // 版本+1在mapper中实现，此处只作为条件
            accountBalanceInfo.setVersionNumber(accountBalanceInfoDTO.getVersionNumber());
            // 更新
            updateBalanceInfoByClock(accountBalanceInfoDTO);
            // 生成流水
            buildFengBaoGlams(null, accManInfo, today, s, accountBalanceInfoDTO, null);
        });
    }

    private void instFengBao(String orderId, String productCode) {
        List<InstallOrderDTO> orders = getOrder(orderId, null, BackOrOutTableEnum.FENBAO);
        InstallOrderDTO order = orders.get(0);
        if (!StringUtils.equals(order.getAbsStatus(), "N")) {
            logger.error("ABS operation not allowed for repeated processing: orderId={}, absStatus={}", orderId, order.getAbsStatus());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ABS_NOT_ALLOWED_REPEAT_FAULT);
        }
        if (!StringUtils.equals(order.getStatus(), "1")) {
            logger.error("ABS operation not allowed for installment order status: orderId={}, status={}", orderId, order.getStatus());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ABS_NOT_ALLOWED_INST_ORDER_STATUS_IS_ONE_FAULT);
        }
        checkProductCode(productCode, "1");
        AccountManagementInfoDTO accManInfo = getAccountManagementInfoDTO(order.getAccountManagementId());
        if (!StringUtils.equalsAny(accManInfo.getAbsType(), "0", "1")) {
            logger.error("Account has other ABS operations: accountManagementId={}, absType={}", order.getAccountManagementId(), accManInfo.getAbsType());
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCT_EXIST_OTHER_ABS_FAULT);
        }
        LocalDate today = getToday(accManInfo.getCustomerId(), accManInfo.getOrganizationNumber());
        //封包分期处理
        fengBaoInst(orders, accManInfo, productCode, today, "1", "1");
        //管理账户更新
        handleAcctMan(accManInfo, "1", null, null);
        //Abs状态表增加
        addAbsStatus(accManInfo, today, "1", productCode, String.valueOf(order.getOrderId()), null);
    }

    /**
     * 资产编号格式检查
     * @param productCode   资产编号
     * @param balType       abs类型对应的前缀
     */
    private void checkProductCode(String productCode, String balType) {
        if (productCode == null || (productCode.length() != 20 && !productCode.startsWith(balType))) {
            logger.error("Invalid asset package format: productCode={}, balType={}, expectedLength=20", productCode, balType);
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_BAL_TYPE_NOT_CORRECT_FAULT);
        }
    }

    /**
     * 获取管理账户(通过查询数据库)
     * @param accountManagementId
     * @return
     */
    private AccountManagementInfoDTO getAccountManagementInfoDTO(String accountManagementId) {
        return findAccManInfo(accountManagementId);
    }

    /**
     * 封包订单处理
     * @param orders
     * @param accManInfo
     * @param productCode
     * @param today
     * @param absType
     * @param source
     */
    @BatchSharedAnnotation
    private void fengBaoInst(List<InstallOrderDTO> orders, AccountManagementInfoDTO accManInfo,
                             String productCode, LocalDate today, String absType, String source) {
        if (!CollectionUtils.isEmpty(orders)) {
            if ("3".equals(absType)) {
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_ACCT_EXIST_NO_ACCOUNT_INST_FAULT);
            }
            if (StringUtils.equals("2", source)) {
                // 订单筛选
                orders = orders.stream()
                        .filter(installOrder -> StringUtils.equals("N", installOrder.getAbsStatus()))
                        .collect(Collectors.toList());
            }
            for (InstallOrderDTO installOrder : orders) {
                installOrder.setAbsStatus("F");
                installOrder.setAbsProductCode(productCode);
                //分期订单更新
                updateInstallOrder(installOrder);

                // 查询计划
                logger.info("Calling installPlanService.planByOrderId: orderId={}", installOrder.getOrderId());
                List<InstallPlanDTO> installPlanDtos = installPlanService.planByOrderId(installOrder.getOrderId());
                logger.info("installPlanService.planByOrderId completed: orderId={}, resultSize={}", installOrder.getOrderId(), installPlanDtos != null ? installPlanDtos.size() : 0);
                // 批中检查计划是否更新
                if (CustAccountBO.isBatch()) {
                    installPlanDtos = CustAccountBO.threadCustAccountBO.get().getInstallBO().checkInstallPlans(installPlanDtos, installOrder.getOrderId());
                }
                Integer postedTerm = installOrder.getPostedTerm();
                //分期计划更新
                if (!CollectionUtils.isEmpty(installPlanDtos)) {
                    installPlanDtos.forEach(installPlanDTO -> {
                        if (installPlanDTO.getTerm() > postedTerm) {
                            installPlanDTO.setAbsStatus("F");
                            installPlanDTO.setAbsProductCode(productCode);

                            // 更新计划
                            updateInstallPlan(installPlanDTO);
                        }
                    });
                }
                //写会计流水汇总表中
                buildFengBaoGlams(installOrder, accManInfo, today, "1", null, absType);
            }
        }
    }

    /**
     * 新增abs状态表(批次共用)
     * @param accManInfo
     * @param today
     * @param absType
     * @param absProductCode
     * @param orderId
     * @param acctAbsStage
     */
    private void addAbsStatus(AccountManagementInfoDTO accManInfo, LocalDate today, String absType,
                              String absProductCode, String orderId, String acctAbsStage) {
        AccountAbsstatus tAmsAbsstatus = new AccountAbsstatus();
        //todo 待修改 IdGeneratorManager.numberId19Generator().generateId()
        tAmsAbsstatus.setId(0L);
        tAmsAbsstatus.setOrganizationNumber(accManInfo.getOrganizationNumber());
        tAmsAbsstatus.setBranchid("110110");
        tAmsAbsstatus.setAccountManagementId(accManInfo.getAccountManagementId());
        tAmsAbsstatus.setAbsType(absType);
        tAmsAbsstatus.setAssetNo(absProductCode);
        tAmsAbsstatus.setAbsStatus("F");
        tAmsAbsstatus.setOrderId(orderId);
        tAmsAbsstatus.setPreabsDate(today);
        tAmsAbsstatus.setRollbackAbsDate(null);
        tAmsAbsstatus.setOutabsDate(null);
        tAmsAbsstatus.setEndabsDate(null);
        tAmsAbsstatus.setStatus("0");
        tAmsAbsstatus.setCreateTime(LocalDateTime.now());
        tAmsAbsstatus.setUpdateTime(LocalDateTime.now());
        tAmsAbsstatus.setUpdateBy("admin");
        tAmsAbsstatus.setVersionNumber(1L);
        tAmsAbsstatus.setAcctAbsStage(acctAbsStage);

        if (CustAccountBO.isBatch()) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            custAccountBO.addTamsAbsStatus(BeanMapping.copy(tAmsAbsstatus, AccountAbsstatusDTO.class));
        } else {
            accountAbsstatusMapper.insertSelective(tAmsAbsstatus);
        }
    }

    /**
     * 更新管理账户（批中共用）
     * @param accManInfo
     * @param absType
     * @param assetNo
     * @param date
     */
    private void handleAcctMan(AccountManagementInfoDTO accManInfo, String absType, String assetNo, String date) {
        accManInfo.setUpdateTime(LocalDateTime.now());
        accManInfo.setVersionNumber(accManInfo.getVersionNumber() + 1);
        switch (absType) {
            case "1":
                if (!StringUtils.equals(accManInfo.getAbsType(), "1")) {
                    accManInfo.setAbsType(absType);
                }
                break;
            case "2":
                accManInfo.setAbsType(absType);
                accManInfo.setAbsProductCodeFirst(assetNo);
                accManInfo.setAbsProductCodeCurr(assetNo);
                accManInfo.setAbsEndDate(DateHelper.toLocalDate(date));
                break;
            case "3":
                accManInfo.setAbsType(absType);
                accManInfo.setAbsProductCodeFirst(assetNo);
                accManInfo.setAbsProductCodeCurr(assetNo);
                break;
            default:
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.P_ABS_STATUS_NOT_CORRECT_FAULT);
        }

        // 更新
        updateManagementInfo(accManInfo);
    }

    /**
     * 构建封包流水 (批次公用)
     * @param installOrderDTO
     * @param accManInfo
     * @param today
     * @param absType
     * @param accountBalanceInfoDTO
     * @param type
     */
    @BatchSharedAnnotation
    private void buildFengBaoGlams(InstallOrderDTO installOrderDTO,
                                   AccountManagementInfoDTO accManInfo,
                                   LocalDate today,
                                   String absType,
                                   AccountBalanceInfoDTO accountBalanceInfoDTO,
                                   String type) {

        AccountantGlamsDTO glams = new AccountantGlamsDTO();

        // 五级分类
        glams.setFiveTypeIndicator(accManInfo.getFiveTypeIndicator());
        // abs状态
        glams.setAbsType(absType);
        glams.setId(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        glams.setBranchid("110110");
        glams.setAccountManagementId(accManInfo.getAccountManagementId());
        glams.setAcctLogo(accManInfo.getProductNumber());
        glams.setGlobalFlowNo(IdGeneratorManager.sequenceIdGenerator().generateSeqId());
        glams.setPostingDate(today);
        // 管理账户的财务状态为0：正常，编辑为A:正常转ABS
        if ("0".equals(accManInfo.getFinanceStatus())) {
            glams.setFinanceStatus("A");
            // 管理账户的财务状态为3：逾期，编辑为E:逾期转ABS
        } else if ("3".equals(accManInfo.getFinanceStatus())) {
            glams.setFinanceStatus("E");
            // 管理账户的财务状态为1：非应计，编辑为B:非应计转ABS
        } else if ("1".equals(accManInfo.getFinanceStatus())) {
            glams.setFinanceStatus("B");
        } else {
            logger.warn("Abnormal parameter status: accountManagementId={}, financeStatus={}", accManInfo.getAccountManagementId(), accManInfo.getFinanceStatus());
        }

        if (accountBalanceInfoDTO != null) {
            glams.setInterestInd(accountBalanceInfoDTO.getInterestIndicator());
        }

        glams.setPriceTaxFlg("N");
        glams.setTxnInd("3");
        glams.setBalProcessInd("1");
        glams.setOrigTxnAbsInd("0");
        glams.setProcessInd("0");
        glams.setCreateTime(LocalDateTime.now());
        glams.setUpdateBy("admin");
        glams.setUpdateTime(LocalDateTime.now());
        glams.setVersionNumber(1L);
        glams.setChannelId("2");
        //不同字段赋值
        String jzCode = null;
        switch (absType) {
            case "1":
                if ("1".equals(type)) {
                    glams.setModuleFlag("9");
                } else if ("2".equals(type)) {
                    glams.setModuleFlag("B");
                } else if ("3".equals(type)) {
                    glams.setModuleFlag("C");
                }
                glams.setPostingCurrencyCode(installOrderDTO.getInstallmentCcy());
                glams.setPostingAmt(installOrderDTO.getUnpostedAmount());

                logger.info("Calling installProductEnquiryService.installmentProductEnquiry: organizationNumber={}, productCode={}", installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
                InstallParameterDTO installTransEntrys = installProductEnquiryService.installmentProductEnquiry(
                        installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode());
                logger.info("installProductEnquiryService.installmentProductEnquiry completed: organizationNumber={}, productCode={}, result={}", installOrderDTO.getOrganizationNumber(), installOrderDTO.getProductCode(), installTransEntrys != null ? "found" : "not found");
                InstallAccountingTransParmResDTO accountTran = installTransEntrys.getInstallAccountTran();

                String principalTransactionCode = accountTran.getPrincipalTransactionCode();
                //todo 暂时注释 借贷分离改造，交易类型、余额类型、结转交易码如何赋值
               /* TransactionCodeResDTO transactionCode =
                        transactionCodeService.findTransactionCode(accManInfo.getOrganizationNumber(),
                                principalTransactionCode);
                jzCode = getTxnCode(transactionCode.getTransactionTypeCode(), accManInfo);
                glams.setTxnCode(jzCode);
                glams.setTxnCodeOrig(jzCode);

                glams.setOrganizationNumber(installOrderDTO.getOrganizationNumber());
                glams.setAbsStatus(installOrderDTO.getAbsStatus());
                glams.setAssetNo(installOrderDTO.getAbsProductCode());
                glams.setOrderId(String.valueOf(installOrderDTO.getOrderId()));
                logger.info("Calling transactionTypeService.findTransactionType: organizationNumber={}, transactionTypeCode={}", glams.getOrganizationNumber(), transactionCode.getTransactionTypeCode());
                TransactionTypeResDTO transactionType =
                        transactionTypeService.findTransactionType(glams.getOrganizationNumber(),
                                transactionCode.getTransactionTypeCode());
                logger.info("transactionTypeService.findTransactionType completed: organizationNumber={}, transactionTypeCode={}, result={}", glams.getOrganizationNumber(), transactionCode.getTransactionTypeCode(), transactionType != null ? "found" : "not found");
                glams.setBalType(transactionType.getBalType());
                glams.setTransactionAttribute("0");
                glams.setDebitCreditIndicator("T");
                glams.setAmortizeInd("N");
                glams.setTransactionTypeCode(transactionCode.getTransactionTypeCode());*/
                break;
            case "2":
                glams.setModuleFlag("B");
                glams.setPostingCurrencyCode(accountBalanceInfoDTO.getCurrency());
                glams.setPostingAmt(accountBalanceInfoDTO.getBalance());
                jzCode = getTxnCode(accountBalanceInfoDTO.getTransactionTypeCode(), accManInfo);
                glams.setTxnCode(jzCode);
                glams.setTxnCodeOrig(jzCode);
                glams.setOrganizationNumber(accManInfo.getOrganizationNumber());
                glams.setAbsStatus(accountBalanceInfoDTO.getAbsStatus());
                glams.setAssetNo(accountBalanceInfoDTO.getAbsProductCode());
                glams.setOrderId(accountBalanceInfoDTO.getInstallmentOrderNumber());
                logger.info("Calling transactionTypeService.findTransactionType: organizationNumber={}, transactionTypeCode={}", glams.getOrganizationNumber(), accountBalanceInfoDTO.getTransactionTypeCode());
                TransactionTypeResDTO transactionTypeResDTO =
                        transactionTypeService.findTransactionType(glams.getOrganizationNumber(),
                                accountBalanceInfoDTO.getTransactionTypeCode());
                logger.info("transactionTypeService.findTransactionType completed: organizationNumber={}, transactionTypeCode={}, result={}", glams.getOrganizationNumber(), accountBalanceInfoDTO.getTransactionTypeCode(), transactionTypeResDTO != null ? "found" : "not found");
                glams.setBalType(transactionTypeResDTO.getBalType());
                glams.setTransactionAttribute("0");
                glams.setDebitCreditIndicator("T");
                glams.setAmortizeInd("N");
                glams.setTransactionTypeCode(transactionTypeResDTO.getTransactionTypeCode());
                break;
            case "3":
                glams.setModuleFlag("C");
                glams.setPostingCurrencyCode(accountBalanceInfoDTO.getCurrency());
                glams.setPostingAmt(accountBalanceInfoDTO.getBalance());
                jzCode = getTxnCode(accountBalanceInfoDTO.getTransactionTypeCode(), accManInfo);
                glams.setTxnCode(jzCode);
                glams.setTxnCodeOrig(jzCode);

                glams.setOrganizationNumber(accManInfo.getOrganizationNumber());
                glams.setAbsStatus(accountBalanceInfoDTO.getAbsStatus());
                glams.setAssetNo(accountBalanceInfoDTO.getAbsProductCode());
                glams.setOrderId(accountBalanceInfoDTO.getInstallmentOrderNumber());
                logger.info("Calling transactionTypeService.findTransactionType: organizationNumber={}, transactionTypeCode={}", glams.getOrganizationNumber(), accountBalanceInfoDTO.getTransactionTypeCode());
                TransactionTypeResDTO transactionType1 =
                        transactionTypeService.findTransactionType(glams.getOrganizationNumber(),
                                accountBalanceInfoDTO.getTransactionTypeCode());
                logger.info("transactionTypeService.findTransactionType completed: organizationNumber={}, transactionTypeCode={}, result={}", glams.getOrganizationNumber(), accountBalanceInfoDTO.getTransactionTypeCode(), transactionType1 != null ? "found" : "not found");
                glams.setBalType(transactionType1.getBalType());
                glams.setTransactionAttribute("0");
                glams.setDebitCreditIndicator("T");
                glams.setAmortizeInd("N");
                glams.setTransactionTypeCode(transactionType1.getTransactionTypeCode());

                // 利息费用价税分离
                if (StringUtils.equalsAny(glams.getBalType(), "2", "3")) {
                    glams.setPriceTaxFlg("Y");
                }

                glams.setNpasFirst("1");
                break;
            default:
                break;
        }

        // 插入流水
        insertTamsGlams(glams);
    }

    private String getTxnCode(String txnTypeCode, AccountManagementInfoDTO accManInfo) {
        //入账交易交易类型
        logger.info("Calling transactionTypeService.findTransactionType: organizationNumber={}, transactionTypeCode={}", accManInfo.getOrganizationNumber(), txnTypeCode);
        TransactionTypeResDTO transactionType =
                transactionTypeService.findTransactionType(accManInfo.getOrganizationNumber(),
                        txnTypeCode);
        logger.info("transactionTypeService.findTransactionType completed: organizationNumber={}, transactionTypeCode={}, result={}", accManInfo.getOrganizationNumber(), txnTypeCode, transactionType != null ? "found" : "not found");
        return transactionType.getJzCode();
    }

    @BatchSharedAnnotation
    private AccountManagementInfoDTO findAccManInfo(String accountManagementId) {
        logger.info("Query account management info by ID: accountManagementId={}", accountManagementId);
        AccountManagementInfoDTO dto = null;
        try {
            if (CustAccountBO.isBatch()) {
                dto = CustAccountBO.threadCustAccountBO.get().getCustomerBO().getManagementById(accountManagementId);
            } else {
                AccountManagementInfo info =
                        accountManagementInfoMapper.selectByPrimaryKey(accountManagementId);
                if (info != null) {
                    dto = new AccountManagementInfoDTO();
                    BeanCopier copierAccountManagementModelToDTO =
                            BeanCopier.create(AccountManagementInfo.class, AccountManagementInfoDTO.class, false);
                    copierAccountManagementModelToDTO.copy(info, dto, null);
                }
            }
        } catch (Exception e) {
            throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_SELECT_DATABASE_FAULT);
        }
        return dto;
    }


    /**
     * 获取abs状态数据（含批中判断）
     * @param manegementId
     * @param orgNum
     * @param branchId
     * @param productCode
     * @param absType
     * @return
     */
    private AccountAbsstatus getAbsStatus(String manegementId, String orgNum, String branchId, String productCode, String absType){
        // 先尝试从缓存获取(可能获取的是批中已更新的表)
        Optional<AccountAbsstatusDTO> cacheFirstAbsStatus = Optional.empty();
        if (CustAccountBO.isBatch()) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            cacheFirstAbsStatus = custAccountBO.getAbsStatus(manegementId, orgNum, branchId, productCode, absType);
        }
        // 首次资产编号对应状态
        // 不存在缓存查库
        return cacheFirstAbsStatus.isPresent() ?
                BeanMapping.copy(cacheFirstAbsStatus.get(), AccountAbsstatus.class)
                :  accountAbsstatusSelfMapper.selectOneByCondition(manegementId, orgNum, productCode, null, absType, branchId);
    }

    /**
     * 更新abs状态（含批中判断）
     * @param absstatus
     */
    private void updateAbsStatus(AccountAbsstatus absstatus){
        if (CustAccountBO.isBatch()) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            custAccountBO.addTamsAbsStatus(BeanMapping.copy(absstatus, AccountAbsstatusDTO.class));
        } else {
            accountAbsstatusMapper.updateByPrimaryKeySelective(absstatus);
        }
    }

    /**
     * 获取管理账户（含批中判断）
     * @param managementId
     * @return
     */
    private AccountManagementInfoDTO getManagementInfo(String managementId){
        // 管理账户
        AccountManagementInfoDTO accManInfo;
        if (CustAccountBO.isBatch()) {
            CustAccountBO<CustAccountParamBO> custAccountBO = CustAccountBO.threadCustAccountBO.get();
            accManInfo = custAccountBO.getCustomerBO().getManagementById(managementId);
        } else {
            accManInfo = getAccountManagementInfoDTO(managementId);
        }

        return accManInfo;
    }

    /**
     * 更新管理账户（含批中判断）
     * @param managementInfoDTO
     */
    private void updateManagementInfo(AccountManagementInfoDTO managementInfoDTO){
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateManagement(managementInfoDTO);
        } else {
            accountManagementInfoMapper.updateByPrimaryKey(BeanMapping.copy(managementInfoDTO, AccountManagementInfo.class));
        }
    }

    /**
     * 插入会计传票（含批中判断）
     * @param tAmsGlvcher
     */
    private void insertTamsGlvcher(AccountantGlvcher tAmsGlvcher){
        // 批次放入缓存
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getInstallBO().insertGlvcher(BeanMapping.copy(tAmsGlvcher, AccountantGlvcherDTO.class));
        } else {
            tAmsGlvcherMapper.insertSelective(tAmsGlvcher);
        }
    }

    /**
     * 更新会计资产传票（含批中判断）
     * @param tAmsGlvcherabs
     */
    private void updateTamsGlvcherAbs(AccountantGlvcherAbs tAmsGlvcherabs){
        // 批次放入缓存
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getInstallBO().updateAbsGlvcher(BeanMapping.copy(tAmsGlvcherabs, AccountantGlvcherAbsDTO.class));
        } else {
            try {
                tAmsGlvcherAbsMapper.updateByPrimaryKeySelective(tAmsGlvcherabs);
            } catch (Exception e) {
                logger.error("Failed to update asset accounting voucher table: accountManagementId={}", accountManagementId, e);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
            }
        }
    }

    /**
     * 更新分期订单（含批中判断）
     * @param installOrder
     */
    @BatchSharedAnnotation
    private void updateInstallOrder(InstallOrderDTO installOrder){
        // 批次放入缓存
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallOrder(installOrder);
        } else {
            try {
                installOrderMapper.updateByPrimaryKey(BeanMapping.copy(installOrder, InstallOrder.class));
            } catch (Exception e) {
                logger.error("Failed to update installment order table: orderId={}", installOrder.getOrderId(), e);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
            }
        }
    }

    /**
     * 更新分期计划（含批中判断）
     * @param installPlan
     */
    @BatchSharedAnnotation
    private void updateInstallPlan(InstallPlanDTO installPlan){
        // 批次放入缓存
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getInstallBO().updateInstallPlan(installPlan);
        } else {
            try {
                installPlanMapper.updateByPrimaryKey(BeanMapping.copy(installPlan, InstallPlan.class));
            } catch (Exception e) {
                logger.error("Failed to update installment plan table: planId={}", installPlan.getPlanId(), e);
                throw new AnyTxnInstallException(AnyTxnInstallRespCodeEnum.D_UPDATE_DATABASE_FAULT);
            }
        }
    }

    /**
     * 插入流水（含批中判断）
     * @param tAmsGlams
     */
    @BatchSharedAnnotation
    private void insertTamsGlams(AccountantGlamsDTO tAmsGlams){
        // 批次放入缓存
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().insertAccountantGlamsList(tAmsGlams);
        } else {
            amsGlamsMapper.insert(BeanMapping.copy(tAmsGlams, AccountantGlams.class));
        }
    }

    /**
     * 更新交易账户（含批中判断）
     * @param accountBalanceInfo
     */
    private void updateBalanceInfoByClock(AccountBalanceInfoDTO accountBalanceInfo){
        if (CustAccountBO.isBatch()) {
            CustAccountBO.threadCustAccountBO.get().getCustomerBO().updateBalanceInfo(accountBalanceInfo, true);
        } else {
            int i = accountBalanceInfoSelfMapper.updateByOptLock(BeanMapping.copy(accountBalanceInfo, AccountBalanceInfo.class));
            if (i == 0) {
                logger.warn("Failed to update account balance info: transactionBalanceId={}, version={}", accountBalanceInfo.getTransactionBalanceId(), accountBalanceInfo.getVersionNumber());
            }
        }
    }

}
