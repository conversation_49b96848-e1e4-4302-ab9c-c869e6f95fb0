package com.anytech.anytxn.transaction.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.account.enums.FinanceStatusEnum;
import com.anytech.anytxn.business.base.account.enums.InterestRateFlagEnum;
import com.anytech.anytxn.business.base.account.enums.TransactionTypeCodeEnum;
import com.anytech.anytxn.business.base.common.constants.MaintenanceConstant;
import com.anytech.anytxn.business.base.common.domain.dto.MaintenanceLogDTO;
import com.anytech.anytxn.business.base.common.service.IMaintenanceLogBisService;
import com.anytech.anytxn.business.base.transaction.domain.dto.LargeGraceBO;
import com.anytech.anytxn.business.base.transaction.domain.dto.LimitControlUnitDTO;
import com.anytech.anytxn.business.dao.account.model.AccountDynamicSumConditions;
import com.anytech.anytxn.business.dao.account.mapper.AccountManagementInfoSelfMapper;
import com.anytech.anytxn.business.base.monetary.service.ICustReconciliationControlService;
import com.anytech.anytxn.central.service.rule.PricingRuleService;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.limit.base.constant.LimitConstant;
import com.anytech.anytxn.parameter.base.common.domain.dto.AccountBalanceDTO;
import com.anytech.anytxn.parameter.base.common.service.IAccountBalanceService;
import com.anytech.anytxn.transaction.base.constants.Constants;
import com.anytech.anytxn.transaction.base.enums.DebitCreditIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.ReversalIndicatorEnum;
import com.anytech.anytxn.transaction.base.enums.StartDateOptionEnum;
import com.anytech.anytxn.transaction.base.constants.TransRuleConstants;
import com.anytech.anytxn.transaction.base.constants.TransactionConstants;
import com.anytech.anytxn.transaction.base.enums.AccountStatusEnum;
import com.anytech.anytxn.transaction.base.enums.AllocationTypeEnum;
import com.anytech.anytxn.transaction.base.enums.AnyTxnTransactionRespCodeEnum;
import com.anytech.anytxn.transaction.base.enums.CreateMethodEnum;
import com.anytech.anytxn.transaction.base.enums.TransactionAttributeEnum;
import com.anytech.anytxn.transaction.base.service.IDebitTransactionService;
import com.anytech.anytxn.transaction.base.service.IGlAmsService;
import com.anytech.anytxn.transaction.base.service.IInterestAccureService;
import com.anytech.anytxn.transaction.base.service.ILargeGraceService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedAccountCacheService;
import com.anytech.anytxn.transaction.service.batchpost.accountcache.TxnRecordedCacheAccountBalanceService;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.PartitionKeyUtils;
import com.anytech.anytxn.business.base.accounting.domain.dto.AccountantGlamsDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountBalanceInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.dao.account.model.AccountBalanceInfo;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.dao.account.model.AccountStatisticsInfo;
import com.anytech.anytxn.business.base.monetary.domain.dto.CustReconciliationControlDTO;
import com.anytech.anytxn.business.dao.transaction.model.PaymentAllocationHistory;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionTypeResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.ITransactionTypeService;
import com.anytech.anytxn.common.rule.dto.DataInputDTO;
import com.anytech.anytxn.common.rule.matcher.RuleMatcherManager;
import com.anytech.anytxn.common.rule.matcher.TxnRuleMatcher;
import com.anytech.anytxn.transaction.base.domain.bo.InterestProcessResultBO;
import com.anytech.anytxn.transaction.base.domain.bo.OverPaymentBO;
import com.anytech.anytxn.transaction.base.domain.bo.RecordedMidBO;
import com.anytech.anytxn.transaction.base.enums.InterestIndicatorEnum;
import com.anytech.anytxn.transaction.base.exception.AnyTxnTransactionException;
import com.anytech.anytxn.transaction.service.transcommon.TransAccountStatisticsService;
import com.anytech.anytxn.transaction.service.transcommon.TransTraceLogService;
import com.anytech.anytxn.transaction.base.utils.CalculateDateUtils;
import com.anytech.anytxn.transaction.base.utils.TransPartitionKeyHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 借记交易/贷记还原交易入账处理
 *
 * <AUTHOR>
 * @date 2018-08-30 11:44
 **/
@Service
public class DebitTransactionServiceImpl implements IDebitTransactionService {

    private static final Logger logger = LoggerFactory.getLogger(DebitTransactionServiceImpl.class);
    @Autowired
    private IInterestAccureService interestAccureService;
    @Autowired
    private ITransactionCodeService transactionCodeService;
    @Autowired
    private ITransactionTypeService transactionTypeService;
    @Autowired
    private IGlAmsService glAmsService;
    @Autowired
    private OuterLimitInit outerLimitInit;
    @Autowired
    private IAccountBalanceService accountBlanceService;
    @Autowired
    private TxnRecordedAccountCacheService txnRecordedAccountCacheService;
    @Autowired
    private TxnRecordedCacheAccountBalanceService txnRecordedCacheAccountBalanceService;
    @Autowired
    private TransAccountStatisticsService transAccountStatisticsService;
    @Autowired
    private ILargeGraceService largeGraceService;
    @Autowired
    private TransTraceLogService transTraceLogService;
    @Autowired
    private PricingRuleService pricingRuleService;
    @Autowired
    private ICustReconciliationControlService custReconciliationControlService;
    @Autowired
    private AccountManagementInfoSelfMapper accountManagementInfoSelfMapper;
    @Autowired
    private IMaintenanceLogBisService maintenanceLogService;
    @Autowired
    private SequenceIdGen sequenceIdGen;

    /**
     * 借记交易账户操作入口
     *
     * @param recordedMidBO 接口数据
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public void doDebitAccount(RecordedMidBO recordedMidBO, CustReconciliationControlDTO controlDTO) {
        logger.info("Enter debit transaction posting logic. cardNumber={}, transactionCode={}", recordedMidBO.recorded.getTxnCardNumber(),
                recordedMidBO.recorded.getTxnTransactionCode());
        List<LimitControlUnitDTO> limitControlUnits = recordedMidBO.recorded.getLimitControlUnits();
        List<LimitControlUnitDTO> limitControlUnitBOS = new ArrayList<>(16);
        if (!CollectionUtils.isEmpty(limitControlUnits) && limitControlUnits.size() > 0) {
            if (limitControlUnits.size() > 1) {
                Map<String, List<LimitControlUnitDTO>> collect = limitControlUnits.stream()
                        .filter(c -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(c.getLimitUnitCode()))
                        .collect(Collectors.groupingBy(c -> c.getLimitUnitCode() + ";" +
                                c.getLimitUnitVersion() + ";" + c.getTransactionType() + ";" +
                                c.getLimitUseOrder()));
                for (Map.Entry<String, List<LimitControlUnitDTO>> map : collect.entrySet()) {
                    List<LimitControlUnitDTO> value = map.getValue();
                    BigDecimal reduce = value.stream().map(LimitControlUnitDTO::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    LimitControlUnitDTO limitControlUnitBO = new LimitControlUnitDTO();
                    limitControlUnitBO.setAmount(reduce);
                    limitControlUnitBO.setLimitUnitCode(value.get(0).getLimitUnitCode());
                    limitControlUnitBO.setLimitUnitVersion(value.get(0).getLimitUnitVersion());
                    limitControlUnitBO.setTransactionType(value.get(0).getTransactionType());
                    limitControlUnitBO.setLimitUseOrder(value.get(0).getLimitUseOrder());
                    limitControlUnitBO.setRecordTransactionBO(value.get(0).getRecordTransactionBO());
                    limitControlUnitBOS.add(limitControlUnitBO);
                }
            } else {
                limitControlUnitBOS = limitControlUnits;
            }
        }
        for (LimitControlUnitDTO limitControlUnit : limitControlUnitBOS) {
            if (limitControlUnit.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            boolean graceFlag = largeGraceService.setDebitLargeGraceInfo(recordedMidBO, controlDTO, limitControlUnit);
//            if (!graceFlag) {
//                return ;
//            }

            doDebitAccount(recordedMidBO, limitControlUnit);
        }
        //手续费相关信息
        buildOverPaymentOrDraft(recordedMidBO);
        //处理借记或贷记全汇总统计账户
        handleDebitOrCreditSumAcctStatis(recordedMidBO);

    }

    /**
     * 借记交易交易交易类型入账
     *
     * @param recordedMidBO       RecordedBO
     * @param limitControlUnit LimitControlUnitBO
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public void doDebitAccount(RecordedMidBO recordedMidBO, LimitControlUnitDTO limitControlUnit) {
        logger.info("Enter debit transaction posting logic. cardNumber={}, transactionType={}", recordedMidBO.recorded.getTxnCardNumber(),
                limitControlUnit.getTransactionType());

        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        //更改状态
        if (AccountStatusEnum.NEW.getCode().equals(accountManagementInfoDTO.getAccountStatus()) || AccountStatusEnum.SLEEP.getCode().equals(accountManagementInfoDTO.getAccountStatus())) {
            AccountManagementInfoDTO old = BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfoDTO.class);
            accountManagementInfoDTO.setAccountStatus(AccountStatusEnum.ACTIVE.getCode());
            LocalDate today = recordedMidBO.organizationInfoResDTO.getToday();
            accountManagementInfoDTO.setAccountStatusSetDate(today);
            txnRecordedAccountCacheService.accountManagementInfoUpdateByPrimaryKey(BeanMapping.copy(accountManagementInfoDTO, AccountManagementInfo.class));
            //写入操作维护变更日志
            MaintenanceLogDTO maintenanceLog = new MaintenanceLogDTO();
            maintenanceLog.setOperationTimestamp(today.atStartOfDay());
            maintenanceLog.setPrimaryKeyValue(accountManagementInfoDTO.getAccountManagementId());
            maintenanceLog.setOperationType(MaintenanceConstant.OPERATION_U);
            maintenanceLog.setTransactionDataType(MaintenanceConstant.DATA_A);
            maintenanceLog.setOperatorId(LoginUserUtils.getLoginUserName());
            maintenanceLogService.add(maintenanceLog, accountManagementInfoDTO, old, MaintenanceConstant.ACCOUNT_MANAGEMENT_INFO);
        }
        //判断交易类型的余额借贷记方向
        TransactionTypeResDTO transactionTypeRes = recordedMidBO.getTransactionTypeResMap().get(limitControlUnit.getTransactionType());
        //更新普通交易账户
        AccountBalanceInfoDTO accountBalanceInfoDto = handleAccountBalanceInfo(recordedMidBO,limitControlUnit);
        //更新一般统计账户(借记)
        handleCommonAsi(recordedMidBO, limitControlUnit);
        if (Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(), transactionTypeRes.getBalanceLoanDirection())) {
            //借记
            //所有交易均触发利息回算
            logger.info("Calling interestAccureService.backInterest, accountId={}, amount={}", accountBalanceInfoDto.getTransactionBalanceId(), limitControlUnit.getAmount());
            interestAccureService.backInterest(accountBalanceInfoDto, accountManagementInfoDTO, recordedMidBO.recorded,
                    limitControlUnit.getAmount());
            logger.info("interestAccureService.backInterest call completed, accountId={}", accountBalanceInfoDto.getTransactionBalanceId());
        } else if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), transactionTypeRes.getBalanceLoanDirection())) {
            //贷记
            //触发还款分配流水更新处理。
            savePaymentAllocatedHistory(recordedMidBO, accountBalanceInfoDto, limitControlUnit.getAmount());
        }
    }

    /**
     * 处理借记或贷记全汇总统计账户
     *
     * @param recordedMidBO 接口数据
     */
    @Override
    public void handleDebitOrCreditSumAcctStatis(RecordedMidBO recordedMidBO) {
        List<LimitControlUnitDTO> limitControlUnits = recordedMidBO.recorded.getLimitControlUnits();
        //透支部分金额
        BigDecimal overdraftAmout = limitControlUnits.stream().filter(x -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(x.getLimitUnitCode()))
                .filter(x -> Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(),
                recordedMidBO.getTransactionTypeResMap().get(x.getTransactionType()).getBalanceLoanDirection()))
                .map(LimitControlUnitDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //溢缴部分金额
        BigDecimal overRepaymentAmout = limitControlUnits.stream().filter(x -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(x.getLimitUnitCode()))
                .filter(x -> Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(),
                recordedMidBO.getTransactionTypeResMap().get(x.getTransactionType()).getBalanceLoanDirection()))
                .map(LimitControlUnitDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        //查询借方全汇总的统计账户
        logger.info("Calling txnRecordedAccountCacheService.accountStatisticsInfoSelectDebitSum, accountManagementId={}", recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        AccountStatisticsInfo debitSumStatis = txnRecordedAccountCacheService.accountStatisticsInfoSelectDebitSum(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        logger.info("txnRecordedAccountCacheService.accountStatisticsInfoSelectDebitSum call completed, resultId={}", debitSumStatis != null ? debitSumStatis.getStatisticsId() : null);

        //TODO 可能没用，有空删掉试试
        //借方全汇总的统计账户更新
        if ((debitSumStatis == null)) {
            this.saveDebitOrCreditSumAcctStatisticsInfo(recordedMidBO, overdraftAmout, TransactionTypeCodeEnum.DEBIT_SUM.getCode());
        } else {
            this.updateDebitOrCreditSumAcctStatisticsInfo(debitSumStatis, overdraftAmout, TransactionTypeCodeEnum.DEBIT_SUM.getCode());
        }

        //查询贷方全汇总的统计账户
        logger.info("Calling txnRecordedAccountCacheService.accountStatisticsInfoSelectCreditSum, accountManagementId={}", recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        AccountStatisticsInfo creditSumStatis = txnRecordedAccountCacheService.accountStatisticsInfoSelectCreditSum(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        logger.info("txnRecordedAccountCacheService.accountStatisticsInfoSelectCreditSum call completed, resultId={}", creditSumStatis != null ? creditSumStatis.getStatisticsId() : null);
        //贷方全汇总的统计账户更新
        if ((creditSumStatis == null)) {
            this.saveDebitOrCreditSumAcctStatisticsInfo(recordedMidBO, overRepaymentAmout, TransactionTypeCodeEnum.CREDIT_SUM.getCode());
        } else {
            this.updateDebitOrCreditSumAcctStatisticsInfo(creditSumStatis, overRepaymentAmout, TransactionTypeCodeEnum.CREDIT_SUM.getCode());
        }
    }

    /**
     * 更新一般统计账户(借记) (已测)
     *
     * @param recordedMidBO 接口数据
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public void handleCommonAsi(RecordedMidBO recordedMidBO, LimitControlUnitDTO limitControlUnitBO) {
        //查询普通统计账户
        logger.info("Calling txnRecordedAccountCacheService.accountStatisticsInfoSelectByIdAndType, accountManagementId={}, transactionType={}", recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId(), limitControlUnitBO.getTransactionType());
        AccountStatisticsInfo accountStatisticsInfo = txnRecordedAccountCacheService.accountStatisticsInfoSelectByIdAndType(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId(), limitControlUnitBO.getTransactionType());
        logger.info("txnRecordedAccountCacheService.accountStatisticsInfoSelectByIdAndType call completed, resultId={}", accountStatisticsInfo != null ? accountStatisticsInfo.getStatisticsId() : null);
        transAccountStatisticsService.updateSingleAccountStatistics(recordedMidBO, limitControlUnitBO, accountStatisticsInfo);

    }

    /*======================================================逻辑B=======================================================*/

    /**
     * 《逻辑B》：普通交易账户更新处理 (已测)
     *
     * @param recordedMidBO 接口数据
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = AnyTxnTransactionException.class)
    public AccountBalanceInfoDTO handleAccountBalanceInfo(RecordedMidBO recordedMidBO, LimitControlUnitDTO limitControlUnit) {
        String transactionType = limitControlUnit.getTransactionType();
        TransactionTypeResDTO transactionTypeRes = recordedMidBO.transactionTypeResMap.get(transactionType);
        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        String createMethod = CreateMethodEnum.SINGLE.getCode();
        AccountBalanceDTO balanceCollectInfo = null;
        if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), transactionTypeRes.getBalanceLoanDirection())) {
            //贷记默认按全汇总
            createMethod = CreateMethodEnum.ALL.getCode();
        } else {
            String tableId = executeRule(recordedMidBO, TransactionConstants.ACCOUNT_BALANCE_CREATE_RULE, transactionType);
            if (StringUtils.isNotEmpty(tableId)) {
                //根据机构号、上述规则获取到的结果,生效的状态,读取交易账户汇总维度参数表（parm_acct_balance_collect_info）
                balanceCollectInfo = accountBlanceService.findByOrgTableIdAndStatus(accountManagementInfoDTO.getOrganizationNumber(), tableId, Constants.STATUS_EFFECT);
                if (balanceCollectInfo != null) {
                    createMethod = balanceCollectInfo.getCreateMethod();
                }
            }
        }
        recordedMidBO.getTransactionBO().setCreateMethod(createMethod);
        AccountBalanceInfo accountBalanceInfo = new AccountBalanceInfo();

        switch (createMethod) {
            //更新交易账户信息表
            case "0":
                // 1)汇总入账处理
                logger.info("Calling txnRecordedCacheAccountBalanceService.selectByManagementIdAndTransType, accountManagementId={}, transactionTypeCode={}", accountManagementInfoDTO.getAccountManagementId(), transactionTypeRes.getTransactionTypeCode());
                AccountBalanceInfo sumAccountBalanceInfo = txnRecordedCacheAccountBalanceService.selectByManagementIdAndTransType(accountManagementInfoDTO.getAccountManagementId(), transactionTypeRes.getTransactionTypeCode());
                logger.info("txnRecordedCacheAccountBalanceService.selectByManagementIdAndTransType call completed, resultId={}", sumAccountBalanceInfo != null ? sumAccountBalanceInfo.getTransactionBalanceId() : null);

                if (sumAccountBalanceInfo == null) {
                    logger.debug("Summary condition not met, create a new transaction account.");
                    accountBalanceInfo = saveAccountBalanceInfo(recordedMidBO, createMethod, balanceCollectInfo, limitControlUnit);
                } else {
                    logger.debug("Summary condition met, update the matched transaction account: id={}, balance={}", sumAccountBalanceInfo.getTransactionBalanceId(), sumAccountBalanceInfo.getBalance());
                    accountBalanceInfo = updateAccountBalanceInfo(sumAccountBalanceInfo, recordedMidBO, limitControlUnit);
                }
                break;
            case "1":
                // b）单笔交易级余额入账处理
                accountBalanceInfo = saveAccountBalanceInfo(recordedMidBO, createMethod, balanceCollectInfo, limitControlUnit);
                break;
            case "2":
                // 3）日累计交易级余额入账处理
                // 起息日处理
                LocalDate interestStartDate = this.initInterest(recordedMidBO,
                        limitControlUnit.getRecordTransactionBO().getInterestAccrualTableId()).getInterestStartDate();
                AccountBalanceInfo accBa = new AccountBalanceInfo();
                accBa.setCreateMethod(createMethod);
                accBa.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
                accBa.setInterestStartDate(interestStartDate);
                //动态条件
                AccountDynamicSumConditions dynamicSumConditions = new AccountDynamicSumConditions();
                setSummaryConditions(accBa, recordedMidBO, balanceCollectInfo, dynamicSumConditions, limitControlUnit);

                List<AccountBalanceInfo> accountBalanceInfoList = txnRecordedCacheAccountBalanceService.accountBalanceInfoSelectByMassConditions(accBa, dynamicSumConditions);
                AccountBalanceInfo aggAccountBalanceInfo = null;
                if (accountBalanceInfoList != null && !accountBalanceInfoList.isEmpty()) {
                    aggAccountBalanceInfo = accountBalanceInfoList.get(0);
                }

                if (aggAccountBalanceInfo == null) {
                    logger.info("Summary condition not met, create a new transaction account.");
                    accountBalanceInfo = saveAccountBalanceInfo(recordedMidBO, createMethod, balanceCollectInfo, limitControlUnit);
                } else {
                    logger.info("Summary condition met, update the matched transaction account: id={}, balance={}", aggAccountBalanceInfo.getTransactionBalanceId(), aggAccountBalanceInfo.getBalance());
                    accountBalanceInfo = updateAccountBalanceInfo(aggAccountBalanceInfo, recordedMidBO, limitControlUnit);
                }
                break;
            case "3":
                //按账期汇总创建交易级账户
                //满足以下条件时，符合汇总条件：
                //管理账户的上一账单日 < 接口中的入账日
                //（txnBillingDate）<= 下一账单日（中间变量），并且，
                //管理账户的上一账单日 < 交易账户中的创建日期
                //（CREATE_DATE）<= 下一账单日（中间变量），
                LocalDate lastStatementDate = (accountManagementInfoDTO.getLastStatementDate() == null ? LocalDate.of(1, 1, 1) : accountManagementInfoDTO.getLastStatementDate());
                LocalDate nextStatementDate = recordedMidBO.getTransactionBO().getNextStatementDate();
                if (!(lastStatementDate.compareTo(recordedMidBO.recorded.getTxnBillingDate()) < 0
                        && recordedMidBO.recorded.getTxnBillingDate().compareTo(nextStatementDate) <= 0)) {
                    logger.info("Summary condition not met, create a new transaction account.");
                    accountBalanceInfo = saveAccountBalanceInfo(recordedMidBO, createMethod, balanceCollectInfo, limitControlUnit);
                    break;
                }
                AccountBalanceInfo summaryBalance = new AccountBalanceInfo();
                summaryBalance.setCreateMethod(createMethod);
                //动态条件
                AccountDynamicSumConditions dynamicSums = new AccountDynamicSumConditions();
                //固定条件
                setSummaryConditions(summaryBalance, recordedMidBO, balanceCollectInfo, dynamicSums, limitControlUnit);

                List<AccountBalanceInfo> matchAccountBalanceInfoList =
                        txnRecordedCacheAccountBalanceService.accountBalanceInfoSelectCycleAccountBalanceInfoList(summaryBalance, lastStatementDate, nextStatementDate, dynamicSums);
                AccountBalanceInfo matchAccountBalanceInfo = null;
                if (CollectionUtils.isNotEmpty(matchAccountBalanceInfoList)) {
                    matchAccountBalanceInfo = matchAccountBalanceInfoList.get(0);
                }
                if (matchAccountBalanceInfo == null) {
                    logger.info("Summary condition not met, create a new transaction account.");
                    accountBalanceInfo = saveAccountBalanceInfo(recordedMidBO, createMethod, balanceCollectInfo, limitControlUnit);
                } else {
                    logger.info("Summary condition met, update the matched transaction account: id={}, balance={}", matchAccountBalanceInfo.getTransactionBalanceId(), matchAccountBalanceInfo.getBalance());
                    //如果交易账户的创建方式（create_method）= 3，
                    //并且，入账接口中的入账日期（txnBillingDate）- 当前交易账户的利息累积日期（interest_accrue_date）后的差值大于1，触发调用《利息累积处理》
                    if (StringUtils.isNotEmpty(limitControlUnit.getRecordTransactionBO().getInterestAccrualTableId())
                            && matchAccountBalanceInfo.getInterestAccrueDate() != null
                            && recordedMidBO.recorded.getTxnBillingDate().compareTo(matchAccountBalanceInfo.getInterestAccrueDate()) > 1) {
                        InterestProcessResultBO interestProcessResultBO = interestAccureService.accureInterest(BeanMapping.copy(matchAccountBalanceInfo, AccountBalanceInfoDTO.class), accountManagementInfoDTO, recordedMidBO.getRecorded());
                        if (interestProcessResultBO != null) {
                            matchAccountBalanceInfo.setAccrueInterest(interestProcessResultBO.getAccrueInterest());
                            matchAccountBalanceInfo.setLastInterestAccrueDate(interestProcessResultBO.getLastInterestAccrueDate());
                            matchAccountBalanceInfo.setInterestAccrueDate(interestProcessResultBO.getInterestAccrueDate());
                        }
                    }
                    accountBalanceInfo = updateAccountBalanceInfo(matchAccountBalanceInfo, recordedMidBO, limitControlUnit);
                }
                break;
            default:
                logger.error("Unsupported creation method");
                break;
        }

        /* 设置交易账户ID */
        if (accountBalanceInfo != null) {
            limitControlUnit.getRecordTransactionBO().setBalanceId(accountBalanceInfo.getTransactionBalanceId());
        }

        return BeanMapping.copy(accountBalanceInfo, AccountBalanceInfoDTO.class);
    }

    /**
     * 汇总条件
     *
     * @param accBa                AccountBalanceInfo
     * @param recordedMidBO           RecordedBO
     * @param balanceCollectInfo   AccountBalanceDTO
     * @param dynamicSumConditions AccountDynamicSumConditions
     * @param limitControlUnit     LimitControlUnitBO
     * @return AccountBalanceInfo
     */
    private AccountBalanceInfo setSummaryConditions(AccountBalanceInfo accBa, RecordedMidBO recordedMidBO, AccountBalanceDTO balanceCollectInfo,
                                                    AccountDynamicSumConditions dynamicSumConditions, LimitControlUnitDTO limitControlUnit) {
        TransactionTypeResDTO transactionTypeResDTO = recordedMidBO.transactionTypeResMap.get(limitControlUnit.getTransactionType());
        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        accBa.setAccountManagementId(recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId());
        accBa.setTransactionTypeCode(transactionTypeResDTO.getTransactionTypeCode());
        accBa.setOrganizationNumber(accountManagementInfoDTO.getOrganizationNumber());
        accBa.setCurrency(accountManagementInfoDTO.getCurrency());
        accBa.setCurrentEffectInterestRate(limitControlUnit.getRecordTransactionBO().getCurrentEffectInterestRate());
        accBa.setTransactionMinimumPaymentId(limitControlUnit.getRecordTransactionBO().getMixRepayId());
        LargeGraceBO largeGraceBO = limitControlUnit.getRecordTransactionBO().getLargeGraceBO();
        accBa.setLargeGraceIndicator(largeGraceBO.getLargeGraceIndicator());
        accBa.setActualStatementDate(limitControlUnit.getRecordTransactionBO().getLargeGraceBO().getActualStatementDate());
        String interestIndicator = initInterestIndicator(recordedMidBO, transactionTypeResDTO.getBalType(), recordedMidBO.getTransactionCodeResDTO().getTaxFlag());
        accBa.setInterestIndicator(interestIndicator);

        //利息月汇总，增加条件
        if (TransactionConstants.INTEREST_COLLECT_MONTHLY_2.equals(recordedMidBO.organizationInfoResDTO.getInterestCollectMonthlyFlag())) {
            if (StringUtils.isNotEmpty(recordedMidBO.recorded.getTxnOriginalTxnAccountId())) {
                accBa.setOriginalTxnBalanceId(recordedMidBO.recorded.getTxnOriginalTxnAccountId());
            }
        }
        //汇总条件add 额度管控单元编号 版本号
        accBa.setLimitUnitCode(limitControlUnit.getLimitUnitCode());
        accBa.setLimitUnitVersion(limitControlUnit.getLimitUnitVersion());
        //资产出表状态
        accBa.setAbsStatus(initAbsStatus(recordedMidBO));
        ////资产包编号
        accBa.setAbsProductCode(recordedMidBO.recorded.getAbsProductCode());
        //动态条件
        dynamicSumConditions(recordedMidBO, balanceCollectInfo, accBa, dynamicSumConditions);
        // 汇总条件匹配
        logger.info("Summary condition: id={}, type={}, amount={}", accBa.getTransactionBalanceId(), accBa.getTransactionTypeCode(), accBa.getAmount());
        return accBa;
    }

    /**
     * 组装动态汇总
     *
     * @param recordedMidBO         recordedBO
     * @param balanceCollectInfo 交易账户汇总维度参数表
     * @param accBa              交易账户
     */
    private void dynamicSumConditions(RecordedMidBO recordedMidBO, AccountBalanceDTO balanceCollectInfo, AccountBalanceInfo accBa, AccountDynamicSumConditions dynamicSumConditions) {
        //卡产品编号选项
        if ("1".equals(balanceCollectInfo.getCardProductNumberOption())) {
            accBa.setCardProductNumber(recordedMidBO.cardBO.getCardProductNumber());
            dynamicSumConditions.setCardProductNumberOption(true);
        }
        //资产渠道ID选项
        if ("1".equals(balanceCollectInfo.getChannelIdOption())) {
            accBa.setChannelId(recordedMidBO.recorded.getTxnChannelId());
            dynamicSumConditions.setChannelIdOption(true);
        }

        //资产子渠道ID选项
        if ("1".equals(balanceCollectInfo.getSubChannelIdOption())) {
            accBa.setSubChannelId(recordedMidBO.recorded.getTxnSubChannelId());
            dynamicSumConditions.setSubChannelIdOption(true);
        }

        //资金源ID选项
        if ("1".equals(balanceCollectInfo.getFundIdOption())) {
            accBa.setFundId(recordedMidBO.recorded.getTxnFundId());
            dynamicSumConditions.setFundIdOption(true);
        }

        //商户编号选项
        if ("1".equals(balanceCollectInfo.getMerchantIdOption())) {
            accBa.setMerchantId(recordedMidBO.recorded.getTxnMerchantId());
            dynamicSumConditions.setMerchantIdOption(true);
        }
    }


    /**
     * 《逻辑B》：普通交易账户更新处理 (已测)
     * Rule-2.1:
     * 新建单笔交易账户
     */
    private AccountBalanceInfo saveAccountBalanceInfo(RecordedMidBO recordedMidBO, String createMethod,
                                                      AccountBalanceDTO balanceCollectInfo, LimitControlUnitDTO limitControlUnitBO) {
        AccountBalanceInfo accountBalanceInfo = this.initInterest(recordedMidBO,
                limitControlUnitBO.getRecordTransactionBO().getInterestAccrualTableId());
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        /*=====================开始insert账户交易信息准备数据===========================*/
        //主键
        accountBalanceInfo.setTransactionBalanceId(id);
        accountBalanceInfo.setCorporateCustomerId(accountManagementInfoDTO.getCorporateCustomerId());
        accountBalanceInfo.setAccountManagementId(accountManagementInfoDTO.getAccountManagementId());
        TransactionTypeResDTO transactionTypeResDTO = recordedMidBO.getTransactionTypeResMap().get(limitControlUnitBO.getTransactionType());
        accountBalanceInfo.setTransactionTypeCode(transactionTypeResDTO.getTransactionTypeCode());
        accountBalanceInfo.setOrganizationNumber(accountManagementInfoDTO.getOrganizationNumber());
        accountBalanceInfo.setCurrency(accountManagementInfoDTO.getCurrency());
        accountBalanceInfo.setCreateMethod(createMethod);
        accountBalanceInfo.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
        accountBalanceInfo.setAmount(recordedMidBO.recorded.getTxnBillingAmount());
        //余额
        accountBalanceInfo.setBalance(limitControlUnitBO.getAmount());
        //累积利息,赋值为0
        accountBalanceInfo.setAccrueInterest(BigDecimal.ZERO);
        accountBalanceInfo.setAggregateAmount(BigDecimal.ZERO);
        //表内表外标志
        String interestIndicator = initInterestIndicator(recordedMidBO, transactionTypeResDTO.getBalType(), recordedMidBO.getTransactionCodeResDTO().getTaxFlag());
        accountBalanceInfo.setInterestIndicator(interestIndicator);

        accountBalanceInfo.setInterestAccrualTableId(limitControlUnitBO.getRecordTransactionBO().getInterestAccrualTableId());
        accountBalanceInfo.setTransactionMinimumPaymentId(limitControlUnitBO.getRecordTransactionBO().getMixRepayId());
        accountBalanceInfo.setInterestSettleTableId(limitControlUnitBO.getRecordTransactionBO().getInterestSettleTableId());
        accountBalanceInfo.setBalanceNetoutTableId(limitControlUnitBO.getRecordTransactionBO().getBalanceNetoutTableId());
        accountBalanceInfo.setCurrentEffectInterestRate(limitControlUnitBO.getRecordTransactionBO().getCurrentEffectInterestRate());

        accountBalanceInfo.setParentTransactionBalanceId(recordedMidBO.recorded.getTxnParentTransactionAccountId());
        //待定
        accountBalanceInfo.setCreditLimitNodeId("待定");
        //分期订单号
        //光大POC
        if (recordedMidBO.recorded != null && recordedMidBO.recorded.getTxnInstallmentOrderId() != null) {
            accountBalanceInfo.setInstallmentOrderNumber(recordedMidBO.recorded.getTxnInstallmentOrderId());
        } else {
            accountBalanceInfo.setInstallmentOrderNumber("0");
        }
        accountBalanceInfo.setStatus(AccountStatusEnum.SLEEP.getCode());
        accountBalanceInfo.setPartitionKey(PartitionKeyUtils.IntPartitionKey(accountManagementInfoDTO.getCustomerId()));
        accountBalanceInfo.setCreateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        accountBalanceInfo.setVersionNumber(1L);
        accountBalanceInfo.setCustomerId(accountManagementInfoDTO.getCustomerId());

        //超长免息相关字段赋值
        setLargeGraceInfo(accountBalanceInfo, limitControlUnitBO.getRecordTransactionBO().getLargeGraceBO());

        //利息月汇总，原始交易id
        String originalTxnAccountId = getOriginalTxnBalanceId(recordedMidBO.recorded.getTxnOriginalTxnAccountId()
                , accountBalanceInfo.getTransactionBalanceId());
        accountBalanceInfo.setOriginalTxnBalanceId(originalTxnAccountId);

        //周期汇总相关字段
        //卡产品编号
        String cardProductNumber = null;
        if (recordedMidBO.cardBO != null) {
            cardProductNumber = recordedMidBO.cardBO.getCardProductNumber();
        }

        //资产渠道ID
        String channelId = recordedMidBO.recorded.getTxnChannelId();
        //资产子渠道ID
        String subChannelId = recordedMidBO.recorded.getTxnSubChannelId();
        //资金源ID
        String fundId = recordedMidBO.recorded.getTxnFundId();
        //商户编号
        String merchantId = recordedMidBO.recorded.getTxnMerchantId();
        //资产出表状态
        String absStatus = initAbsStatus(recordedMidBO);
        //资产包编号
        String absProductCode = recordedMidBO.recorded.getAbsProductCode();

        if (!("0".equals(createMethod) || "1".equals(createMethod))) {
            if ("0".equals(balanceCollectInfo.getCardProductNumberOption())) {
                cardProductNumber = Constants.DEFAULT_CARD_PRODUCT_NUMBER;
            }
            if ("0".equals(balanceCollectInfo.getChannelIdOption())) {
                channelId = "**********";
            }
            if ("0".equals(balanceCollectInfo.getSubChannelIdOption())) {
                subChannelId = "**********";
            }
            if ("0".equals(balanceCollectInfo.getFundIdOption())) {
                fundId = "**********";
            }
            if ("0".equals(balanceCollectInfo.getMerchantIdOption())) {
                merchantId = "**********00000";
            }

        }
        //卡产品编号
        accountBalanceInfo.setCardProductNumber(cardProductNumber);
        //资产渠道ID
        accountBalanceInfo.setChannelId(channelId);
        //资产子渠道ID
        accountBalanceInfo.setSubChannelId(subChannelId);
        //资金源ID
        accountBalanceInfo.setFundId(fundId);
        //商户编号
        accountBalanceInfo.setMerchantId(merchantId);
        //资产出表状态
        accountBalanceInfo.setAbsStatus(absStatus);
        //资产包编号
        accountBalanceInfo.setAbsProductCode(absProductCode);
        //固定利率标识（有两个值：0-利率固定和 1-利率可变）
        accountBalanceInfo.setInterestRateFlag(
                InterestRateFlagEnum.VARIABLE.getCode());

        //借记交易 外部额度更新接口组装
        outerLimitInit.debitSaveBalanceChangeInit(recordedMidBO, accountBalanceInfo, limitControlUnitBO);

        //余额变动会计流水
        AccountantGlamsDTO tAmsGlams = glAmsService.buildGlAms(recordedMidBO, BeanMapping.copy(accountBalanceInfo, AccountBalanceInfoDTO.class), limitControlUnitBO.getAmount());
        logger.info("Calling glAmsService.buildGlAms, accountId={}, amount={}", accountBalanceInfo.getTransactionBalanceId(), limitControlUnitBO.getAmount());
        logger.info("glAmsService.buildGlAms call completed, glAmsId={}", tAmsGlams != null ? tAmsGlams.getId() : null);
        /*=====================结束insert账户交易信息准备数据==========================="*/
        try {
            txnRecordedCacheAccountBalanceService.insert(accountBalanceInfo);

            txnRecordedAccountCacheService.saveGlAms(tAmsGlams);
        } catch (Exception e) {
            logger.error("method:saveAccountBalanceInfo,exception:", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR, e);
        }
        return accountBalanceInfo;
    }

    /**
     * 初始化表内表外标志
     *
     * @param recordedMidBO
     * @return
     */
    private String initInterestIndicator(RecordedMidBO recordedMidBO, String balType, String taxFlag) {
        //表内表外标志
        String interestIndicator = InterestIndicatorEnum.NO.getCode();
        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        if ("2".equals(balType)) {
            if ("0".equals(taxFlag)) {
                //当前核算状态为应计，定位到的交易账户的表内表外标志必须为已交增值税
                if (FinanceStatusEnum.NORMAL.getCode().equals(accountManagementInfoDTO.getFinanceStatus())
                        || FinanceStatusEnum.OVERDUE.getCode().equals(accountManagementInfoDTO.getFinanceStatus())) {
                    interestIndicator = InterestIndicatorEnum.TAX.getCode();
                } else {
                    interestIndicator = InterestIndicatorEnum.NO_TAX.getCode();
                }
            } else {
                interestIndicator = InterestIndicatorEnum.NO.getCode();
            }
        }
        if ("3".equals(balType)) {
            if ("0".equals(taxFlag)) {
                if (FinanceStatusEnum.NORMAL.getCode().equals(accountManagementInfoDTO.getFinanceStatus())
                        || FinanceStatusEnum.OVERDUE.getCode().equals(accountManagementInfoDTO.getFinanceStatus())
                        || FinanceStatusEnum.NONACCRUAL.getCode().equals(accountManagementInfoDTO.getFinanceStatus())) {
                    interestIndicator = InterestIndicatorEnum.TAX.getCode();
                } else {
                    interestIndicator = InterestIndicatorEnum.NO_TAX.getCode();
                }
            } else {
                interestIndicator = InterestIndicatorEnum.NO.getCode();
            }
        }
        return interestIndicator;
    }

    /**
     * 初始化资产出表状态
     *
     * @param recordedMidBO
     * @return
     */
    private String initAbsStatus(RecordedMidBO recordedMidBO) {
        //资产出表状态
        String absStatus = "N";

        if (StringUtils.equalsAny(recordedMidBO.recorded.getAbsStatus(), "A", "F")
                && recordedMidBO.recorded.getAbsProductCode() != null) {
            absStatus = recordedMidBO.recorded.getAbsStatus();
        }
        return absStatus;
    }

    /**
     * 超长免息相关字段赋值
     *
     * @param accountBalanceInfo 交易账户
     */
    private void setLargeGraceInfo(AccountBalanceInfo accountBalanceInfo, LargeGraceBO largeGraceBO) {
        //0-未出账单
        accountBalanceInfo.setStatementIndicator(largeGraceBO.getStatementIndicator());
        accountBalanceInfo.setLargeGraceIndicator(largeGraceBO.getLargeGraceIndicator());
        accountBalanceInfo.setActualStatementDate(largeGraceBO.getActualStatementDate());
        accountBalanceInfo.setActualInterestBillingDate(largeGraceBO.getActualInterestBillingDate());
    }

    /**
     * 《逻辑B》：普通交易账户更新处理 (已测)
     * Rule-2.2
     * 更新一般交易账户
     */
    private AccountBalanceInfo updateAccountBalanceInfo(AccountBalanceInfo accountBalanceInfo, RecordedMidBO recordedMidBO, LimitControlUnitDTO limitControlUnitBO) {
        logger.info("Start updating transaction account: [{}].", accountBalanceInfo.getTransactionBalanceId());
        BigDecimal beforeValue = accountBalanceInfo.getBalance();
        TransactionTypeResDTO transactionTypeRes = recordedMidBO.getTransactionTypeResMap().get(limitControlUnitBO.getTransactionType());
        if (Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(), transactionTypeRes.getBalanceLoanDirection())) {
            //贷记利息
            //溢缴款交易账户的利息累积日期小于（入账日期-1天）
            LocalDate txnBillingDate = recordedMidBO.recorded.getTxnBillingDate();
            InterestProcessResultBO interestProcessResultBO = interestAccureService.accureOverpayInterest(accountBalanceInfo, txnBillingDate, recordedMidBO.organizationInfoResDTO);
            if (null != interestProcessResultBO) {
                //溢缴款累积基数
                accountBalanceInfo.setAggregateAmount(interestProcessResultBO.getAccrueInterest());
                //上一利息累积日期
                accountBalanceInfo.setLastInterestAccrueDate(interestProcessResultBO.getLastInterestAccrueDate());
                //利息累积日期
                accountBalanceInfo.setInterestAccrueDate(interestProcessResultBO.getInterestAccrueDate());
            }
            accountBalanceInfo.setBalance(accountBalanceInfo.getBalance().subtract(limitControlUnitBO.getAmount()));
        } else {
            accountBalanceInfo.setBalance(accountBalanceInfo.getBalance().add(limitControlUnitBO.getAmount()));
        }
        accountBalanceInfo.setAmount(accountBalanceInfo.getAmount().add(limitControlUnitBO.getAmount()));

        //余额变动会计流水
        AccountantGlamsDTO tAmsGlams = glAmsService.buildGlAms(recordedMidBO, BeanMapping.copy(accountBalanceInfo, AccountBalanceInfoDTO.class), limitControlUnitBO.getAmount());
        logger.info("Calling glAmsService.buildGlAms, accountId={}, amount={}", accountBalanceInfo.getTransactionBalanceId(), limitControlUnitBO.getAmount());
        logger.info("glAmsService.buildGlAms call completed, glAmsId={}", tAmsGlams != null ? tAmsGlams.getId() : null);
        accountBalanceInfo.setUpdateTime(LocalDateTime.now());
        accountBalanceInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        try {
            txnRecordedCacheAccountBalanceService.updateByPrimaryKey(accountBalanceInfo);
            transTraceLogService.updateAccountBalance(recordedMidBO, beforeValue, accountBalanceInfo);

            txnRecordedAccountCacheService.saveGlAms(tAmsGlams);
        } catch (Exception e) {
            logger.error("method:updateAccountBalanceInfo,exception:", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);

        }
        //调用外部额度更新接口
        AccountBalanceInfo accountBalanceInfoNew = BeanMapping.copy(accountBalanceInfo, AccountBalanceInfo.class);
        accountBalanceInfoNew.setBalance(limitControlUnitBO.getAmount());
        accountBalanceInfoNew.setLimitUnitCode(limitControlUnitBO.getLimitUnitCode());
        accountBalanceInfoNew.setLimitUnitVersion(limitControlUnitBO.getLimitUnitVersion());
        outerLimitInit.debitUpdateBalanceChangeInit(recordedMidBO, accountBalanceInfoNew);
        return accountBalanceInfo;
    }

//    /**
//     * Rule-1.1
//     * 更新溢缴款账户  (已测)
//     *
//     * @param accountBalanceInfoDTO 溢缴款账户
//     * @param recordedBO         接口数据
//     */
//    @Override
//    public void updateOverPaymentAccountBalanceInfo(AccountBalanceInfoDTO accountBalanceInfoDTO, RecordedBO recordedBO) {
//        log.info("开始更新溢缴款交易账户:[{}]。", accountBalanceInfoDTO.getTransactionBalanceId());
//        //贷记利息
//        InterestProcessResultBO interestProcessResultBO = interestAccureService.accureOverpayInterest(BeanMapping.copy(accountBalanceInfoDTO,AccountBalanceInfo.class), recordedBO.recorded.getTxnBillingDate(), recordedBO.organizationInfoResDTO);
//        if(null != interestProcessResultBO){
//            //溢缴款累积基数
//            accountBalanceInfoDTO.setAggregateAmount(interestProcessResultBO.getAccrueInterest());
//            //上一利息累积日期
//            accountBalanceInfoDTO.setLastInterestAccrueDate(interestProcessResultBO.getLastInterestAccrueDate());
//            //利息累积日期
//            accountBalanceInfoDTO.setInterestAccrueDate(interestProcessResultBO.getInterestAccrueDate());
//        }
//        BigDecimal balance = accountBalanceInfoDTO.getBalance();
//        BigDecimal overRepaymentAmount = this.getOverRepaymentAmount(recordedBO, accountBalanceInfoDTO);
//        accountBalanceInfoDTO.setBalance(overRepaymentAmount);
//        //余额变动流水
//        AccountantGlamsDTO tAmsGlams = glAmsService.buildGlAms(recordedBO, accountBalanceInfoDTO, balance.subtract(overRepaymentAmount));
//        AccountManagementInfoDTO accountManagementInfoDTO = recordedBO.accountBO.getAccountManagementInfo();
//        //判断交易类型是否为利息
//        TransactionCodeResDTO transactionCode = transactionCodeService.findTransactionCode(accountManagementInfoDTO.getOrganizationNumber(),
//                        recordedBO.recorded.getTxnTransactionCode());
//        TransactionTypeResDTO transactionType = transactionTypeService.findTransactionType(accountManagementInfoDTO.getOrganizationNumber(),
//                        transactionCode.getTransactionTypeCode());
//
//        AccountantGlamsDTO otherAmsGlams = null;
//        if (!FinanceStatus.NORMAL.getCode().equals(accountManagementInfoDTO.getFinanceStatus()) && "2".equals(transactionType.getBalType())) {
//            //衍生会计流水
//            otherAmsGlams = glAmsService.buildOtherGlAms(recordedBO, accountBalanceInfoDTO,tAmsGlams.getPostingAmt());
//        }
//        accountBalanceInfoDTO.setUpdateTime(LocalDateTime.now());
//        accountBalanceInfoDTO.setUpdateBy(TransactionConstants.DEFAULT_USER);
//        try {
//            txnRecordedAccountCacheService.saveGlAms(tAmsGlams);
//            if (otherAmsGlams != null) {
//                txnRecordedAccountCacheService.saveGlAms(otherAmsGlams);
//            }
//            txnRecordedCacheAccountBalanceService.updateByPrimaryKey(BeanMapping.copy(accountBalanceInfoDTO, AccountBalanceInfo.class));
//        } catch (Exception e) {
//            log.error("调用[{}]更新表[{}]溢缴款交易账户失败,错误信息[{}]", "updateByPrimaryKeySelective", "ACCOUNT_BALANCE_INFO", e);
//            throw new AnyTxnTransactionException(AnyTxnTransactionRespCode.D_DATABASE_UPDATE_ERROR,e);
//        }
//    }
    /*======================================================逻辑B=======================================================*/

    /*======================================================逻辑C=======================================================*/
    /*======================================================逻辑D=======================================================*/

    /**
     * 保存借记或贷记全汇总统计账户
     *
     * @param recordedMidBO          接口数据
     * @param changeAmount        变动的金额
     * @param transactionTypeCode 交易类型
     */
    private void saveDebitOrCreditSumAcctStatisticsInfo(RecordedMidBO recordedMidBO, BigDecimal changeAmount, String transactionTypeCode) {
        AccountStatisticsInfo accountStatisticsInfo = new AccountStatisticsInfo();
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        String mid = recordedMidBO.accountBO.getAccountManagementInfo().getAccountManagementId();
        String customId = recordedMidBO.accountBO.getAccountManagementInfo().getCustomerId();
        int partition = TransPartitionKeyHelper.getPartitionKeyInt(customId);
        accountStatisticsInfo.setStatisticsId(id);
        accountStatisticsInfo.setAccountManagementId(mid);
        accountStatisticsInfo.setCustomerId(customId);
        accountStatisticsInfo.setCorporateCustomerId(recordedMidBO.getRecorded().getCorporateCustomerId());
        accountStatisticsInfo.setTransactionTypeCode(transactionTypeCode);
        accountStatisticsInfo.setOrganizationNumber(
                recordedMidBO.accountBO.getAccountManagementInfo().getOrganizationNumber());
        accountStatisticsInfo.setCurrency(recordedMidBO.accountBO.getAccountManagementInfo().getCurrency());
        accountStatisticsInfo.setCreateDate(recordedMidBO.recorded.getTxnBillingDate());
        if (Objects.equals(TransactionTypeCodeEnum.DEBIT_SUM.getCode(), transactionTypeCode)) {
            //借方交易账户中的余额
            accountStatisticsInfo.setBalance(changeAmount);
        } else if (Objects.equals(TransactionTypeCodeEnum.CREDIT_SUM.getCode(), transactionTypeCode)) {
            //贷方交易账户中的余额
            accountStatisticsInfo.setBalance(changeAmount);
        }
        accountStatisticsInfo.setStatementBalance(BigDecimal.ZERO);
        accountStatisticsInfo.setLastStatementDate(TransactionConstants.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityDate(TransactionConstants.INITIAL_DATE);
        accountStatisticsInfo.setLastActivityAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCycleToDateCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleDebitAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setLastCycleCreditAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setDebitTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setCreditTotalAmount(BigDecimal.ZERO);
        accountStatisticsInfo.setPartitionKey(partition);
        accountStatisticsInfo.setCreateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);
        accountStatisticsInfo.setVersionNumber(1L);
        txnRecordedAccountCacheService.accountStatisticsInfoInsertSelective(accountStatisticsInfo);

    }

    /**
     * 更新借记或贷记全汇总统计账户
     *
     * @param accountStatisticsInfo 统计账户
     * @param changeAmount          改变的金额
     * @param transactionTypeCode   交易类型
     */
    private void updateDebitOrCreditSumAcctStatisticsInfo(AccountStatisticsInfo accountStatisticsInfo,
                                                          BigDecimal changeAmount, String transactionTypeCode) {
        if (Objects.equals(TransactionTypeCodeEnum.DEBIT_SUM.getCode(), transactionTypeCode)) {
            //借方交易账户中的余额
            accountStatisticsInfo.setBalance(accountStatisticsInfo.getBalance().add(changeAmount));
        } else if (Objects.equals(TransactionTypeCodeEnum.CREDIT_SUM.getCode(), transactionTypeCode)) {
            //贷方交易账户中的余额
            accountStatisticsInfo.setBalance(accountStatisticsInfo.getBalance().subtract(changeAmount));
        }
        accountStatisticsInfo.setUpdateTime(LocalDateTime.now());
        accountStatisticsInfo.setUpdateBy(TransactionConstants.DEFAULT_USER);

        try {

            txnRecordedAccountCacheService.accountStatisticsInfoUpdateByPrimaryKeySelective(accountStatisticsInfo);

        } catch (Exception e) {
            logger.error("method:updateOverRepaymentAccountStatisticsInfo,exception:{}", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_UPDATE_ERROR, e);

        }

    }
    /*======================================================逻辑D=======================================================*/


    /**
     * Rule-4:还款分配流水更新处理
     *
     * @param recordedMidBO            准备数据
     * @param accountBalanceInfoDTO 溢缴款账户
     */
    private void savePaymentAllocatedHistory(RecordedMidBO recordedMidBO, AccountBalanceInfoDTO accountBalanceInfoDTO,
                                             BigDecimal balance) {
        PaymentAllocationHistory paymentAllocationHistory = new PaymentAllocationHistory();
        String id = sequenceIdGen.generateId(TenantUtils.getTenantId());
        logger.info("Start adding payment allocation history: [{}].", id);
        AccountManagementInfoDTO accountManagementInfoDTO = recordedMidBO.accountBO.getAccountManagementInfo();
        String customerId = accountManagementInfoDTO.getCustomerId();

        int partition = TransPartitionKeyHelper.getPartitionKeyInt(customerId);
        paymentAllocationHistory.setPaymentAllocationId(id);
        paymentAllocationHistory.setGlobalFlowNumber(recordedMidBO.recorded.getTxnGlobalFlowNumber());
        paymentAllocationHistory.setAccountManagementId(accountManagementInfoDTO.getAccountManagementId());
        paymentAllocationHistory.setAllocationSequenceNumber(1L);
        paymentAllocationHistory.setOrganizationNumber(accountManagementInfoDTO.getOrganizationNumber());
        paymentAllocationHistory.setCustomerId(accountManagementInfoDTO.getCustomerId());
        paymentAllocationHistory.setTransactionCode(recordedMidBO.recorded.getTxnTransactionCode());
        paymentAllocationHistory.setTransactionDate(recordedMidBO.recorded.getTxnTransactionDate().toLocalDate());
        paymentAllocationHistory.setBillingAmount(recordedMidBO.recorded.getTxnBillingAmount());
        paymentAllocationHistory.setBillingDate(recordedMidBO.recorded.getTxnBillingDate());
        paymentAllocationHistory.setBillingCurrency(recordedMidBO.recorded.getTxnTransactionCurrency());
        paymentAllocationHistory.setTransactionBalanceId(accountBalanceInfoDTO.getTransactionBalanceId());
        paymentAllocationHistory.setTransactionBalanceType(accountBalanceInfoDTO.getTransactionTypeCode());
        paymentAllocationHistory.setAllocatedAmount(balance);
        paymentAllocationHistory.setInterestParmId(accountBalanceInfoDTO.getInterestAccrualTableId());
        paymentAllocationHistory.setCreditLimitNodeId("待定");
        paymentAllocationHistory.setReversalIndicator(ReversalIndicatorEnum.NORMAL.getCode());
        paymentAllocationHistory.setAllocationType(AllocationTypeEnum.DECREASE.getCode());
        paymentAllocationHistory.setCreateDate(accountBalanceInfoDTO.getCreateDate());
        paymentAllocationHistory.setStatus(accountBalanceInfoDTO.getStatus());
        paymentAllocationHistory.setInterestIndicator(accountBalanceInfoDTO.getInterestIndicator());
        //分区键值
        paymentAllocationHistory.setPartitionKey(partition);
        paymentAllocationHistory.setCreateTime(LocalDateTime.now());
        paymentAllocationHistory.setUpdateTime(LocalDateTime.now());
        paymentAllocationHistory.setUpdateBy(TransactionConstants.DEFAULT_USER);
        paymentAllocationHistory.setVersionNumber(1L);
        try {

            txnRecordedAccountCacheService.paymentAllocationHistoryInsertSelective(paymentAllocationHistory);

        } catch (Exception e) {
            logger.error("method:savePaymentAllocatedHistory,exception:{}", e);
            throw new AnyTxnTransactionException(AnyTxnTransactionRespCodeEnum.D_DATABASE_INSERT_ERROR, e);

        }
    }

    /**
     * 创建交易账户对利息进行初始化处理
     *
     * @param recordedMidBO      准备数据
     * @param interestTableId 利率表id
     * @return 交易账户
     */
    private AccountBalanceInfo initInterest(RecordedMidBO recordedMidBO, String interestTableId) {
        AccountBalanceInfo accountBalanceInfo = new AccountBalanceInfo();
        //上个账单日入账利息
        accountBalanceInfo.setLastCycleBillInterest(BigDecimal.ZERO);
        if (StringUtils.isNotEmpty(interestTableId)) {
            LocalDate interestStartDate = null;

            if (recordedMidBO.accountBO.checkDeriveInterest(recordedMidBO.getTransactionCodeResDTO()) && recordedMidBO.recorded.defCondition() == 1) {
                interestStartDate = recordedMidBO.organizationInfoResDTO.getNextProcessingDay();
            } else {
                //根据机构号和产品信息表中的利率参数表ID（interest_table_id）读取利率参数表（parm_interest），获得起息日选项参数（start_date_option）
                String startDateOption = recordedMidBO.interestAccrualParam.getStartDateOption();
                //起息日：当起息日选项=0,交易日起息;当起息日选项=1,入账日起息
                if (StartDateOptionEnum.TRANSACTION.getCode().equals(startDateOption)) {
                    interestStartDate = recordedMidBO.recorded.getTxnTransactionDate().toLocalDate();
                } else if (StartDateOptionEnum.BILLING.getCode().equals(startDateOption)) {
                    interestStartDate = recordedMidBO.recorded.getTxnBillingDate();
                } else if (StartDateOptionEnum.STATEMENT.getCode().equals(startDateOption)) {
                    interestStartDate = getStatementDate(recordedMidBO, recordedMidBO.getControl());
                }
            }
            if (interestStartDate != null) {
                accountBalanceInfo.setInterestStartDate((interestStartDate));
                //起息日前一天
                LocalDate beforeDay = interestStartDate.minusDays(1);
                //利息累积日期,起息日-1天
                accountBalanceInfo.setInterestAccrueDate(beforeDay);
                //上一利息累积日期,起息日-1天
                accountBalanceInfo.setLastInterestAccrueDate(beforeDay);
                //上一结息日,赋值为0000-00-00
                accountBalanceInfo.setLastInterestBillDate(TransactionConstants.INITIAL_DATE);
                //计息参数id
                accountBalanceInfo.setInterestAccrualTableId(interestTableId);
            }
        }
        return accountBalanceInfo;
    }

    /**
     * 获取账单日+1
     *
     * @param recordedMidBO RecordedBO
     * @param controlDTO CustReconciliationControlDTO
     * @return LocalDate
     */
    private LocalDate getStatementDate(RecordedMidBO recordedMidBO, CustReconciliationControlDTO controlDTO) {
        //下一账单日
        LocalDate nextProcessingDay = recordedMidBO.organizationInfoResDTO.getNextProcessingDay();
        LocalDate today = recordedMidBO.organizationInfoResDTO.getToday();
        LocalDate accruedThruDay = recordedMidBO.organizationInfoResDTO.getAccruedThruDay();
        LocalDate calculateDate;
        AccountManagementInfoDTO accountManagementInfo = recordedMidBO.accountBO.getAccountManagementInfo();
        calculateDate = custReconciliationControlService.getBillingDate(controlDTO, accruedThruDay, today, nextProcessingDay);
        LocalDate nextStatementDate = CalculateDateUtils.calculateDate(calculateDate, recordedMidBO.accountBO.getAccountManagementInfo().getCycleDay());
        if (null == accountManagementInfo.getLastStatementDate() ||
                LocalDate.of(1, 1, 1).compareTo(accountManagementInfo.getLastStatementDate()) == 0) {
            LocalDate maxLastStatementDate = accountManagementInfoSelfMapper.selectMaxLastStatementDateByOrgAndCid(accountManagementInfo.getOrganizationNumber(), accountManagementInfo.getCustomerId());
            logger.info("maxLastStatementDate:{}", maxLastStatementDate);
            if (null != maxLastStatementDate && maxLastStatementDate.compareTo(LocalDate.of(1, 1, 1)) > 0) {
                nextStatementDate = maxLastStatementDate.plusMonths(1);
            }
        } else {
            if (null != accountManagementInfo.getLastStatementDate() && nextStatementDate.compareTo(accountManagementInfo.getLastStatementDate().plusMonths(1)) < 0) {
                nextStatementDate = nextStatementDate.plusMonths(1);
            }
        }
        return nextStatementDate.plusDays(1);
    }

    /**
     * 溢缴透支标志、溢缴部分金额、 透支部分金额：
     *
     * @param recordedMidBO recordedBO
     */
    private void buildOverPaymentOrDraft(RecordedMidBO recordedMidBO) {
        OverPaymentBO overPaymentBO = new OverPaymentBO();
        if (TransactionAttributeEnum.CASH.getCode().equals(recordedMidBO.transactionCodeResDTO.getTransactionAttribute())) {
            List<LimitControlUnitDTO> limitControlUnits = recordedMidBO.recorded.getLimitControlUnits();
            //透支部分金额
            BigDecimal overdraftAmout = limitControlUnits.stream().filter(x -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(x.getLimitUnitCode()))
                    .filter(x -> Objects.equals(DebitCreditIndicatorEnum.DEBIT_INDICATOR.getCode(),
                    recordedMidBO.getTransactionTypeResMap().get(x.getTransactionType()).getBalanceLoanDirection()))
                    .map(LimitControlUnitDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //溢缴部分金额
            BigDecimal overRepaymentAmout = limitControlUnits.stream().filter(x -> !LimitConstant.PA_VIRTUAL_LIMIT_UNIT_CODE.equals(x.getLimitUnitCode()))
                    .filter(x -> Objects.equals(DebitCreditIndicatorEnum.CREDIT_INDICATOR.getCode(),
                    recordedMidBO.getTransactionTypeResMap().get(x.getTransactionType()).getBalanceLoanDirection()))
                    .map(LimitControlUnitDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //溢缴款透支标志
            String overRepaymentFlag = TransactionConstants.OVERREPAYMENT_NULL;
            if (overdraftAmout.compareTo(BigDecimal.ZERO) > 0 && overRepaymentAmout.compareTo(BigDecimal.ZERO) > 0) {
                overRepaymentFlag = TransactionConstants.OVERREPAYMENT_PART;
            } else if (overdraftAmout.compareTo(BigDecimal.ZERO) == 0 && overRepaymentAmout.compareTo(BigDecimal.ZERO) > 0) {
                overRepaymentFlag = TransactionConstants.OVERREPAYMENT_FULL;
            }
            overPaymentBO.setOverRepaymentFlag(overRepaymentFlag);
            overPaymentBO.setOverRepaymentAmout(overRepaymentAmout);
            overPaymentBO.setOverdraftAmout(overdraftAmout);
        }
        recordedMidBO.setOverPaymentBO(overPaymentBO);
    }

    /**
     * 获取原始交易账户id
     *
     * @param txnOriginalTxnAccountId 核心入账接口中的原始交易账户id
     * @param transactionBalanceId    新创建的交易账户id
     * @return String 原始交易账户id
     */
    private String getOriginalTxnBalanceId(String txnOriginalTxnAccountId, String transactionBalanceId) {
        if (StringUtils.isNotEmpty(txnOriginalTxnAccountId)) {
            return txnOriginalTxnAccountId;
        } else {
            return transactionBalanceId;
        }
    }

    /**
     * 调用规则 交易账户创建规则
     *
     * @param recordedMidBO RecordedBO
     */
    private String executeRule(RecordedMidBO recordedMidBO, String ruleType, String transactionType) {
        String attribute = pricingRuleService.getProductAttribute(recordedMidBO.getOrganizationNumber(),
                recordedMidBO.getProductInfoResDTO().getProductNumber());
        //基于规则类型找到规则匹配器
        TxnRuleMatcher ruleMatcher = RuleMatcherManager.getMatcher(ruleType, OrgNumberUtils.getOrg());
        if (ruleMatcher == null) {
            logger.info("The rule for this type is empty, ruleType: {}", ruleType);
            return null;
        }
        //规则因子数据
        Map<String, Object> map = TransRuleConstants.buildTransRuleMap(recordedMidBO, ruleType, transactionType, attribute);
        //规则输入
        DataInputDTO dataInput = new DataInputDTO(map, ruleType);
        //执行优先规则匹配
        Map<String, Object> ruleMap = ruleMatcher.execute(dataInput);
        if (ruleMap != null && !ruleMap.isEmpty()) {

            Map.Entry<String, Object> entry = ruleMap.entrySet().iterator().next();
            logger.info("Rule engine call succeeded! ruleType: {}, data: {}", ruleType, entry.getValue());
            return String.valueOf(entry.getValue());

        }
        logger.warn("No matching rule found! ruleType: {}, data: {}", ruleType, ruleMap);
        return null;
    }
}
